#!/bin/bash

# 测试Gemini官方格式API支持
# 使用方法: ./test_gemini_official.sh

# 配置
API_BASE_URL="http://localhost:3000"
API_KEY="your-api-key-here"

echo "=== 测试Gemini官方格式API支持 ==="
echo "API Base URL: $API_BASE_URL"
echo ""

# 测试1: v1beta/models/gemini-2.5-pro:generateContent
echo "测试1: POST /v1beta/models/gemini-2.5-pro:generateContent"
echo "发送请求..."

RESPONSE1=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
  -X POST "$API_BASE_URL/v1beta/models/gemini-2.5-pro:generateContent" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "Hello, how are you?"
          }
        ]
      }
    ],
    "generationConfig": {
      "maxOutputTokens": 100,
      "temperature": 0.7,
      "topP": 0.9
    }
  }')

echo "响应:"
echo "$RESPONSE1"
echo ""

# 测试2: v1beta/models/gemini-2.5-pro:streamGenerateContent
echo "测试2: POST /v1beta/models/gemini-2.5-pro:streamGenerateContent"
echo "发送流式请求..."

RESPONSE2=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
  -X POST "$API_BASE_URL/v1beta/models/gemini-2.5-pro:streamGenerateContent" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "contents": [
      {
        "role": "user", 
        "parts": [
          {
            "text": "Tell me a short joke"
          }
        ]
      }
    ],
    "generationConfig": {
      "maxOutputTokens": 50,
      "temperature": 0.8
    }
  }')

echo "响应:"
echo "$RESPONSE2"
echo ""

# 测试3: v1/models/gemini-2.5-pro:generateContent (v1版本兼容性)
echo "测试3: POST /v1/models/gemini-2.5-pro:generateContent (v1版本)"
echo "发送请求..."

RESPONSE3=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
  -X POST "$API_BASE_URL/v1/models/gemini-2.5-pro:generateContent" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "What is 2+2?"
          }
        ]
      }
    ]
  }')

echo "响应:"
echo "$RESPONSE3"
echo ""

# 测试4: 错误格式测试
echo "测试4: 错误格式测试 - 缺少action"
echo "发送请求..."

RESPONSE4=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
  -X POST "$API_BASE_URL/v1beta/models/gemini-2.5-pro" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{}')

echo "响应:"
echo "$RESPONSE4"
echo ""

# 测试5: 不支持的action测试
echo "测试5: 不支持的action测试"
echo "发送请求..."

RESPONSE5=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
  -X POST "$API_BASE_URL/v1beta/models/gemini-2.5-pro:unsupportedAction" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{}')

echo "响应:"
echo "$RESPONSE5"
echo ""

echo "=== 测试完成 ==="
