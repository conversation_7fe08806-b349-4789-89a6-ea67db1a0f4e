package controller

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"github.com/songquanpeng/one-api/relay/meta"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/message"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/monitor"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	"github.com/songquanpeng/one-api/relay/channeltype"
	"github.com/songquanpeng/one-api/relay/constant"
	"github.com/songquanpeng/one-api/relay/controller"
	_ "github.com/songquanpeng/one-api/relay/controller"
	relayModel "github.com/songquanpeng/one-api/relay/model"
	"github.com/songquanpeng/one-api/relay/relaymode"

	"regexp"
	"strings"
	"sync"
	"time"
)

var revivesAfterFailureMap sync.Map
var revivesAbilityAfterFailureMap sync.Map

// https://platform.openai.com/docs/api-reference/chat

// 这个我不用,跟我那个relayByDifferentMode重复了
func relayHelper(c *gin.Context, relayMode int) *relayModel.ErrorWithStatusCode {
	var err *relayModel.ErrorWithStatusCode
	switch relayMode {
	case relaymode.ImagesGenerations:
		err, _, _ = controller.RelayImageHelper(c, relayMode)
	case relaymode.AudioSpeech:
		fallthrough
	case relaymode.AudioTranslation:
		fallthrough
	case relaymode.AudioTranscription:
		err = controller.RelayAudioHelper(c, relayMode)
	case relaymode.Proxy:
		err = controller.RelayProxyHelper(c, relayMode)
	default:
		err, _, _ = controller.RelayTextHelper(c)
	}
	return err
}

func Relay(c *gin.Context) {
	ctx := c.Request.Context()
	if config.DebugEnabled {
		requestBody, _ := common.GetRequestBody(c)
		logger.Debugf(ctx, "request body: %s", string(requestBody))
	}
	channelId := c.GetInt(ctxkey.ChannelId)
	userId := c.GetInt(ctxkey.Id)

	// 获取请求模型 - 超时处理现在由中间件统一处理
	requestModel := c.GetString(ctxkey.RequestModel)

	// 提前获取用户配额信息
	userQuota, quotaExpireTime, checkErr := model.CacheGetUserQuotaAndExpireTime(c.Request.Context(), userId)
	if checkErr != nil {
		err := openai.ErrorWrapper(checkErr, "get_user_quota_failed", http.StatusInternalServerError)
		handleSpecialError(c, err, "获取用户配额失败", "")
		return
	}
	// 基础配额检查
	if userQuota <= 0 {
		err := openai.ErrorWrapper(
			errors.New(fmt.Sprintf("用户 [%d] 配额不足", userId)),
			"insufficient_quota",
			http.StatusForbidden,
		)

		if config.RootUserRelayErrorNotificationEnabled {
			message.NotifyRootUser(
				"用户配额不足提醒",
				fmt.Sprintf("用户[%d]配额不足", userId),
			)
		}

		handleSpecialError(c, err, "用户配额不足", "")
		return
	}

	// 检查配额是否过期
	if config.QuotaExpireEnabled && quotaExpireTime > 0 && quotaExpireTime < helper.GetTimestamp() {
		err := openai.ErrorWrapper(
			errors.New(fmt.Sprintf("用户 [%d] 配额已过期", userId)),
			"user_quota_expired",
			http.StatusForbidden,
		)
		handleSpecialError(c, err, "用户配额已过期", "")
		return
	}

	// 将配额信息存入context供后续使用
	c.Set(ctxkey.UserQuota, userQuota)
	c.Set(ctxkey.QuotaExpireTime, quotaExpireTime)

	// 记录初次请求时间
	_firstStartTime := helper.GetTimestamp()
	c.Set(ctxkey.FirstStartTime, _firstStartTime)
	bodyCopy, err1 := common.PreserveRequestBody(c)
	if err1 != nil {
		logger.SysError(fmt.Sprintf("failed to preserve request body: %s", err1.Error()))
	}
	err, isTimeout, relayMeta := relayByDifferentMode(c)
	channelId = c.GetInt(ctxkey.ChannelId)
	channelName := c.GetString(ctxkey.ChannelName)
	retryInterval := c.GetInt(ctxkey.RetryInterval)
	undeadModeEnabled := c.GetBool(ctxkey.UndeadModeEnabled)
	tokenBillingType := c.GetInt(ctxkey.TokenBillingType)
	inputHasFunctionCall := c.GetBool(ctxkey.InputHasFunctionCall)
	inputHasImage := c.GetBool(ctxkey.ImageSupported)
	group := c.GetString(ctxkey.Group)
	tokenGroup := c.GetString(ctxkey.TokenGroup)
	if tokenGroup != "" {
		group = tokenGroup
	}
	requestModel = c.GetString(ctxkey.RequestModel)
	if retryInterval == 0 {
		retryInterval = 300
	}
	// 判断最大Prompt长度
	bodyForLog := string(bodyCopy)
	bodyForLog = model.TruncateOptimized(bodyForLog, config.MaxPromptLogLength, requestModel)

	// 记录渠道性能指标 - 处理超时情况
	if isTimeout {
		// 记录超时失败
		if config.ChannelMetricsEnabled && !relayMeta.IsSay1Matched {
			helper.SafeGoroutine(func() {
				metricsCache := model.GetMetricsStorage()
				// 使用从请求开始到现在的时间作为响应时间
				responseTime := helper.GetTimestamp() - relayMeta.StartTime_
				metricsCache.RecordRequest(channelId, requestModel, false, responseTime)
			})
		}

		HandleTimeoutError(c, channelId, channelName, retryInterval, bodyForLog)
		return
	}

	// 记录渠道性能指标 - 处理错误情况
	if err != nil {
		c.Set(ctxkey.FailedDuration, helper.GetTimestamp()-relayMeta.StartTime_)
		relayMeta.TotalDuration = helper.GetTimestamp() - relayMeta.StartTime_
		if config.RootUserRelayErrorNotificationEnabled {
			// 通知管理员
			subject := fmt.Sprintf("用户id[%d] 通道「%s」（#%d）请求失败提醒", userId, channelName, channelId)
			content := fmt.Sprintf("用户id[%d] 通道「%s」（#%d）请求失败，原因：%s, status_code:%d, OpenAIError.Type:%s,OpenAIError.Code:%s 导致报错的请求体为: %s", userId, channelName, channelId, err.Message, err.StatusCode, err.Error.Type, err.Error.Code, bodyForLog)
			message.NotifyRootUser(subject, content)
		}

		// 判断是否是特殊错误,如果是特殊错误则直接返回给用户,无需继续重试了
		if !monitor.IsRelayErrForceRetry(err, requestModel) && monitor.IsSpecialError(err, _firstStartTime) {
			handleSpecialError(c, err, "抛出到下游错误,特殊错误直接返回给用户,无需继续重试", bodyForLog)
			return
		}

		// 判断是否强制抛出错误
		if monitor.IsRelayErrForceThrowError(err, requestModel) {
			handleSpecialError(c, err, "抛出到下游错误,强制抛出错误设置生效,直接返回给用户,无需继续重试", bodyForLog)
			return
		}

		logger.Error(c.Request.Context(), fmt.Sprintf("relay error (channel #%d),err.StatusCode (%d),err.Code(%s),err.Type(%s),err.Error.Code (%s),err.Error.Type (%s),err.Error.Message (%s): %s",
			channelId, err.StatusCode, err.Code, err.Type, err.Error.Code, err.Error.Type, err.Error.Message, err.Message))
		// https://platform.openai.com/docs/guides/error-codes/api-errors
		disableChannelErr := HandleDisableChannelReturnError(c, err, channelId, channelName, retryInterval, undeadModeEnabled, requestModel, bodyForLog)
		if disableChannelErr != nil {
			logger.Error(c.Request.Context(), fmt.Sprintf("disable channel error (channel #%d): %s", channelId, disableChannelErr.Error()))
		}
		if _, ok := c.Get(ctxkey.SpecificChannelId); ok {
			handleMaxRetryTimes(c, err, bodyForLog)
			return
		}
		// 记录请求失败的性能指标
		if config.ChannelMetricsEnabled {
			helper.SafeGoroutine(func() {
				metricsCache := model.GetMetricsStorage()
				// 使用从请求开始到现在的时间作为响应时间
				responseTime := helper.GetTimestamp() - relayMeta.StartTime_
				metricsCache.RecordRequest(channelId, requestModel, false, responseTime)
			})
		}
		// 重试逻辑挪到最下面,为了避免之前渠道未能关闭,导致重试时还是会报错
		// 不采用重定向方式重试
		retryTimes := config.RetryTimes
		if retryTimes > 0 {
			// 构造排除的id
			excludeIds := make([]int, 0)
			if config.RetryWithoutFailedChannelEnabled {
				excludeIds = append(excludeIds, channelId)
			}
			// 在开始重试之前初始化重试记录
			relayMeta.RetryTimestamps = make([]meta.RetryTimestamp, 0)
			// 记录第一次失败的渠道信息
			relayMeta.RetryTimestamps = append(relayMeta.RetryTimestamps, meta.RetryTimestamp{
				ChannelId: int64(channelId),
				StartTime: relayMeta.StartTime_,
				EndTime:   helper.GetTimestamp(),
				Duration:  helper.GetTimestamp() - relayMeta.StartTime_,
			})
			c.Set(ctxkey.RetryTimestamps, relayMeta.RetryTimestamps)

			// 如果开启了保持计费类型一致性，则获取第一次使用的渠道计费类型
			retryTokenBillingType := tokenBillingType
			// 使用便捷函数判断是否应该保持重试计费类型一致性
			shouldKeepBillingType := model.ShouldKeepRetryBillingTypeFromContext(c)

			if shouldKeepBillingType {
				// 直接从context中获取第一次请求使用的渠道计费类型，避免数据库查询
				if firstChannelBillingType := c.GetInt(ctxkey.BillingType); firstChannelBillingType != 0 {
					retryTokenBillingType = firstChannelBillingType
				}
			}

			// 循环retryTimes次
			for i := 0; i < retryTimes; i++ {
				// 需要判断common.RetryWithoutFailedChannelEnabled是否开启,如果开启,则需要排除当前失败的渠道
				var nextRetryChannel *model.Channel // 声明一个指向Channel类型的指针
				var err2 error

				if retryTokenBillingType == common.BillingTypeByQuotaFirst {
					// 如果是按量优先,则优先选择按量计费的渠道
					nextRetryChannel, err2 = model.CacheGetRandomSatisfiedChannel(group, requestModel, false, common.BillingTypeByQuota, inputHasFunctionCall, inputHasImage, excludeIds, c.GetBool(ctxkey.IsV1MessagesPath), userId)
					if err2 != nil {
						// 如果按量计费的渠道没有,则选择按次计费的渠道
						nextRetryChannel, err2 = model.CacheGetRandomSatisfiedChannel(group, requestModel, false, common.BillingTypeByCount, inputHasFunctionCall, inputHasImage, excludeIds, c.GetBool(ctxkey.IsV1MessagesPath), userId)
					}
				} else if retryTokenBillingType == common.BillingTypeByCountFirst {
					// 如果是按次优先,则优先选择按次计费的渠道
					nextRetryChannel, err2 = model.CacheGetRandomSatisfiedChannel(group, requestModel, false, common.BillingTypeByCount, inputHasFunctionCall, inputHasImage, excludeIds, c.GetBool(ctxkey.IsV1MessagesPath), userId)
					if err2 != nil {
						// 如果按次计费的渠道没有,则选择按量计费的渠道
						nextRetryChannel, err2 = model.CacheGetRandomSatisfiedChannel(group, requestModel, false, common.BillingTypeByQuota, inputHasFunctionCall, inputHasImage, excludeIds, c.GetBool(ctxkey.IsV1MessagesPath), userId)
					}
				} else {
					// 如果不是以上两种情况,则直接选择对应的计费模式
					nextRetryChannel, err2 = model.CacheGetRandomSatisfiedChannel(group, requestModel, false, retryTokenBillingType, inputHasFunctionCall, inputHasImage, excludeIds, c.GetBool(ctxkey.IsV1MessagesPath), userId)
				}
				if nextRetryChannel == nil {
					handleMaxRetryTimes(c, err, bodyForLog)
					return
				}
				if err2 != nil {
					logger.SysError(fmt.Sprintf("failed to get next retry channel: %s", err2.Error()))
					if model.ShouldLogDownstreamError(userId) {
						logMessage := &model.ErrorLogMessage{
							Description: "抛出到下游错误failed to get next retry channel",
							DownstreamError: &model.ErrorInfo{
								StatusCode: err.StatusCode,
								Error: model.ErrorDetailInfo{
									Message: err.Error.Message,
									Type:    err.Error.Type,
									Code:    err.Error.Code,
								},
							},
							OriginalError: &model.ErrorInfo{
								StatusCode: err.StatusCode,
								Error: model.ErrorDetailInfo{
									Message: err.Error.Message,
									Type:    err.Error.Type,
									Code:    err.Error.Code,
								},
							},
							RequestParams: relayMeta.DetailPrompt,
						}
						logJson, _ := json.Marshal(logMessage)
						model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, string(logJson), relayMeta.DetailPrompt)
					}
					c.JSON(err.StatusCode, gin.H{
						"error": err.Error,
					})
					return
				}
				excludeIds = append(excludeIds, nextRetryChannel.Id)
				c.Set(ctxkey.UseChannel, excludeIds)
				relayMeta.UseChannel = excludeIds
				c.Set(ctxkey.Channel, nextRetryChannel.Type)
				c.Set(ctxkey.ChannelId, nextRetryChannel.Id)
				c.Set(ctxkey.ChannelName, nextRetryChannel.Name)
				c.Set(ctxkey.BillingType, nextRetryChannel.BillingType)
				c.Set(ctxkey.FunctionCallEnabled, nextRetryChannel.GetFunctionCallEnabled())
				c.Set(ctxkey.ImageSupported, nextRetryChannel.GetImageSupported())
				c.Set(ctxkey.ModelMapping, nextRetryChannel.GetModelMapping())
				c.Set(ctxkey.ModelMappingArr, nextRetryChannel.GetModelMappingArr())
				c.Set(ctxkey.OriginalModel, requestModel) // for retry
				c.Set(ctxkey.ExcludedFields, nextRetryChannel.GetExcludedFields())
				c.Set(ctxkey.ExcludedResponseFields, nextRetryChannel.GetExcludedResponseFields())
				c.Set(ctxkey.ExtraFields, nextRetryChannel.GetExtraFields())
				c.Set(ctxkey.BaseURL, nextRetryChannel.GetBaseURL())
				c.Set(ctxkey.RetryInterval, nextRetryChannel.GetRetryInterval())
				c.Set(ctxkey.UndeadModeEnabled, nextRetryChannel.GetUndeadModeEnabled())
				cfg, _ := nextRetryChannel.LoadConfig()

				if nextRetryChannel.Key != "" {
					c.Request.Header.Set("Authorization", fmt.Sprintf("Bearer %s", nextRetryChannel.Key))
				} else {
					c.Request.Header.Del("Authorization")
				}
				if nextRetryChannel.OpenAIOrganization != nil {
					c.Request.Header.Set("OpenAI-Organization", *nextRetryChannel.OpenAIOrganization)
				}
				// this is for backward compatibility
				switch nextRetryChannel.Type {
				case channeltype.Azure:
					if cfg.APIVersion == "" && nextRetryChannel.Other != nil {
						cfg.APIVersion = *nextRetryChannel.Other
					}
				case channeltype.Xunfei:
					if cfg.APIVersion == "" && nextRetryChannel.Other != nil {
						cfg.APIVersion = *nextRetryChannel.Other
					}
				case channeltype.Gemini:
					if cfg.APIVersion == "" && nextRetryChannel.Other != nil {
						cfg.APIVersion = *nextRetryChannel.Other
					}
				case channeltype.AIProxyLibrary:
					if cfg.LibraryID == "" && nextRetryChannel.Other != nil {
						cfg.LibraryID = *nextRetryChannel.Other
					}
				case channeltype.Ali:
					if cfg.Plugin == "" && nextRetryChannel.Other != nil {
						cfg.Plugin = *nextRetryChannel.Other
					}
				}
				c.Set(ctxkey.Config, cfg)
				// 根据channelId获取channelExtend
				nextRetryChannelExtend, _ := model.CacheGetChannelExByChannelId(nextRetryChannel.Id)
				if nextRetryChannelExtend != nil {
					c.Set("filter_stream_ad", nextRetryChannelExtend.FilterStreamAd)
					c.Set("filter_stream_ad_min_size", nextRetryChannelExtend.FilterStreamAdMinSize)
					c.Set("filter_non_stream_ad", nextRetryChannelExtend.FilterNonStreamAd)
					c.Set("filter_non_stream_ad_regex", nextRetryChannelExtend.FilterNonStreamAdRegex)
					c.Set("filter_system_prompt", nextRetryChannelExtend.FilterSystemPrompt)
					c.Set("custom_system_prompt", nextRetryChannelExtend.CustomSystemPrompt)
					c.Set("extra_headers", nextRetryChannelExtend.GetExtraHeaders())
					c.Set("platform_access_token", nextRetryChannelExtend.PlatformAccessToken)
					c.Set("parse_url_to_content", nextRetryChannelExtend.ParseUrlToContent)
					c.Set("parse_url_prefix_enabled", nextRetryChannelExtend.ParseUrlPrefixEnabled)
					c.Set("parse_url_prefix", nextRetryChannelExtend.ParseUrlPrefix)
					c.Set("custom_full_url_enabled", nextRetryChannelExtend.CustomFullUrlEnabled)
					c.Set("arrange_messages", nextRetryChannelExtend.ArrangeMessages)
					c.Set("original_model_pricing", nextRetryChannelExtend.OriginalModelPricing)
					c.Set("negative_optimization_enabled", nextRetryChannelExtend.NegativeOptimizationEnabled)
					c.Set("negative_optimization_time", nextRetryChannelExtend.NegativeOptimizationTime)
					c.Set("negative_random_offset", nextRetryChannelExtend.NegativeRandomOffset)
					c.Set("original_model_fake_resp_enabled", nextRetryChannelExtend.OriginalModelFakeRespEnabled)
					c.Set("fake_completion_id_enabled", nextRetryChannelExtend.FakeCompletionIdEnabled)
					c.Set("exclude_custom_prompt_cost_enabled", nextRetryChannelExtend.ExcludeCustomPromptCostEnabled)
					c.Set("force_chat_url_enabled", nextRetryChannelExtend.ForceChatUrlEnabled)
					c.Set("ignore_fc_tc_enabled", nextRetryChannelExtend.IgnoreFcTcEnabled)
					c.Set("channel_timeout_breaker_time", nextRetryChannelExtend.ChannelTimeoutBreakerTime)
					c.Set("usage_recalculation_enabled", nextRetryChannelExtend.UsageRecalculationEnabled)
					c.Set("empty_response_error_enabled", nextRetryChannelExtend.EmptyResponseErrorEnabled)
					c.Set("remove_image_download_error_enabled", nextRetryChannelExtend.RemoveImageDownloadErrorEnabled)
					c.Set(ctxkey.Base64ImagePrefixMapping, nextRetryChannelExtend.GetBase64ImagePrefixMapping())
					c.Set("request_token_limit_enabled", nextRetryChannelExtend.RequestTokenLimitEnabled)
					c.Set("min_request_token_count", nextRetryChannelExtend.MinRequestTokenCount)
					c.Set("max_request_token_count", nextRetryChannelExtend.MaxRequestTokenCount)
					c.Set("claude_stream_enabled", nextRetryChannelExtend.ClaudeStreamEnabled)
					c.Set("keyword_error_enabled", nextRetryChannelExtend.KeywordErrorEnabled)
					c.Set("keyword_error", nextRetryChannelExtend.KeywordError)
					c.Set("think_tag_processing_enabled", nextRetryChannelExtend.ThinkTagProcessingEnabled)
				}
				// 在重新请求之前，重新设置请求体
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyCopy))
				logContent := fmt.Sprintf("渠道id[%d]名称[%s]失败后第[%d]次重试，失败原因是:[%v],当前实际请求渠道[%d]: %s", channelId, channelName, i+1, err, nextRetryChannel.Id, nextRetryChannel.Name)
				model.RecordLogToDBAndFileByMeta(c, model.LogTypeSystemInfo, true, relayMeta.DeepCopyToLogMeta(), logContent, 0, relayMeta.TotalDuration, relayMeta.TotalDuration, relayMeta.TotalDuration)
				// 记录本次重试的开始时间
				retryStartTime := helper.GetTimestamp()
				err, isTimeout, relayMeta = relayByDifferentMode(c)
				// 记录本次重试的结束时间和耗时
				retryEndTime := helper.GetTimestamp()
				relayMeta.RetryTimestamps = append(relayMeta.RetryTimestamps, meta.RetryTimestamp{
					ChannelId: int64(nextRetryChannel.Id),
					StartTime: retryStartTime,
					EndTime:   retryEndTime,
					Duration:  retryEndTime - retryStartTime,
				})
				c.Set(ctxkey.RetryTimestamps, relayMeta.RetryTimestamps)
				if err == nil {
					if isTimeout {
						// 记录超时失败
						if config.ChannelMetricsEnabled && !relayMeta.IsSay1Matched {
							helper.SafeGoroutine(func() {
								metricsCache := model.GetMetricsStorage()
								// 使用从请求开始到现在的时间作为响应时间
								responseTime := helper.GetTimestamp() - relayMeta.StartTime_
								metricsCache.RecordRequest(nextRetryChannel.Id, requestModel, false, responseTime)
							})
						}
						HandleTimeoutError(c, nextRetryChannel.Id, nextRetryChannel.Name, retryInterval, bodyForLog)
					}

					return
				} else {
					c.Set(ctxkey.FailedDuration, helper.GetTimestamp()-relayMeta.StartTime_)
					relayMeta.TotalDuration = helper.GetTimestamp() - relayMeta.StartTime_
					// 判断是否是特殊错误,如果是特殊错误则直接返回给用户,无需继续重试了
					if !monitor.IsRelayErrForceRetry(err, requestModel) && monitor.IsSpecialError(err, _firstStartTime) {
						handleSpecialError(c, err, "抛出到下游错误,特殊错误直接返回给用户,无需继续重试", bodyForLog)
						return
					}
					// 判断是否强制抛出错误
					if monitor.IsRelayErrForceThrowError(err, requestModel) {
						handleSpecialError(c, err, "抛出到下游错误,强制抛出错误设置生效,直接返回给用户,无需继续重试", bodyForLog)
						return
					}
				}
				disableChannelErr := HandleDisableChannelReturnError(c, err, nextRetryChannel.Id, nextRetryChannel.Name, nextRetryChannel.GetRetryInterval(), nextRetryChannel.GetUndeadModeEnabled(), requestModel, bodyForLog)
				if disableChannelErr != nil {
					logger.Error(c.Request.Context(), fmt.Sprintf("disable channel error (channel #%d): %s", channelId, disableChannelErr.Error()))
				}
				// 记录请求失败的性能指标
				if config.ChannelMetricsEnabled && !relayMeta.IsSay1Matched {
					helper.SafeGoroutine(func() {
						metricsCache := model.GetMetricsStorage()
						// 使用从请求开始到现在的时间作为响应时间
						responseTime := helper.GetTimestamp() - _firstStartTime
						metricsCache.RecordRequest(nextRetryChannel.Id, requestModel, false, responseTime)
					})
				}
			}
			handleMaxRetryTimes(c, err, bodyForLog)
		} else {
			handleMaxRetryTimes(c, err, bodyForLog)
		}
	}
}

func handleMaxRetryTimes(c *gin.Context, err *relayModel.ErrorWithStatusCode, prompt string) {
	if err.StatusCode == http.StatusTooManyRequests {
		err.Error.Message = "当前分组上游负载已饱和，请稍后再试"
	}

	// 克隆原始错误对象用于日志记录
	originalError := *err

	// 添加新的错误类型屏蔽逻辑 - 只处理 Error.Type
	if config.HideUpstreamApiTypeErrorEnabled {
		// 先检查是否在自定义屏蔽列表中
		isCustomError := false
		for _, customErrorType := range config.CustomHideApiErrorTypes {
			if err.Error.Type == customErrorType {
				isCustomError = true
				break
			}
		}

		// 使用配置的后缀匹配或自定义类型匹配
		if isCustomError || strings.HasSuffix(err.Error.Type, config.ApiErrorTypeSuffix) {
			// 记录原始错误类型到日志
			logger.SysError(fmt.Sprintf("HideUpstreamApiTypeErrorEnabled: original error type: %s replaced to shell_api_error", err.Error.Type))
			err.Error.Type = "shell_api_error"
		}
	}

	// 原有的 HideRelayErrorEnabled 逻辑
	if config.HideRelayErrorEnabled {
		realHideErr := true
		if len(config.HideRelayErrorExceptList) > 0 {
			for _, confErrMsg := range config.HideRelayErrorExceptList {
				if confErrMsg != "" && strings.Contains(err.Error.Message, confErrMsg) {
					realHideErr = false
				}
			}
		}
		if realHideErr {
			originalMsg := err.Error.Message
			if err.StatusCode != http.StatusTooManyRequests {
				logger.SysError(fmt.Sprintf("HideRelayErrorEnabled err==>%s", err.Error.Message))
				err.Error.Message = ""
			}
			// 检查是否在自定义屏蔽列表中
			isCustomError := false
			for _, customErrorType := range config.CustomHideApiErrorTypes {
				if err.Error.Type == customErrorType {
					isCustomError = true
					break
				}
			}
			// 使用配置的后缀匹配或自定义类型匹配
			if isCustomError || strings.HasSuffix(err.Error.Type, config.ApiErrorTypeSuffix) {
				err.Error.Type = "shell_api_error"
			}
			c.JSON(err.StatusCode, gin.H{
				"error": err.Error,
			})
			userId := c.GetInt(ctxkey.Id)
			if model.ShouldLogDownstreamError(userId) {
				logMessage := &model.ErrorLogMessage{
					Description: "抛出到下游错误",
					DownstreamError: &model.ErrorInfo{
						StatusCode: err.StatusCode,
						Error: model.ErrorDetailInfo{
							Message: err.Error.Message,
							Type:    err.Error.Type,
							Code:    err.Error.Code,
						},
					},
					OriginalError: &model.ErrorInfo{
						StatusCode: originalError.StatusCode,
						Error: model.ErrorDetailInfo{
							Message: originalError.Error.Message,
							Type:    originalError.Error.Type,
							Code:    originalError.Error.Code,
						},
					},
					RequestParams: prompt,
				}
				logJson, _ := json.Marshal(logMessage)
				model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, string(logJson), prompt)
			}
			// 记录系统警告日志
			warnLogMessage := &model.ErrorLogMessage{
				Description: "重试超过最大限度,此异常会抛出给调用者:HideRelayErrorEnabled",
				DownstreamError: &model.ErrorInfo{
					StatusCode: err.StatusCode,
					Error: model.ErrorDetailInfo{
						Message: err.Error.Message,
						Type:    err.Error.Type,
						Code:    err.Error.Code,
					},
				},
				OriginalError: &model.ErrorInfo{
					StatusCode: err.StatusCode,
					Error: model.ErrorDetailInfo{
						Message: originalMsg, // 使用原始错误信息
						Type:    err.Error.Type,
						Code:    err.Error.Code,
					},
				},
				RequestParams: prompt,
			}
			warnLogJson, _ := json.Marshal(warnLogMessage)
			model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeSystemWarn, string(warnLogJson), prompt)
			return
		}
	}

	// 屏蔽分组名称的正则表达式
	groupNamePattern := `((?:当前|所有)(?:令牌)?分组\s+)[^\s]+(\s+(?:下|对于))|(\s+分组\s+)[^\s]+(\s+下)`
	groupRegex, errGroup := regexp.Compile(groupNamePattern)
	if errGroup != nil {
		logger.SysError(fmt.Sprintf("errGroup==>failed to compile regex: %s", errGroup.Error()))
	} else {
		err.Error.Message = groupRegex.ReplaceAllString(err.Error.Message, "${1}***${2}${3}***${4}")
	}

	// 替换请求ID
	reReqId, errReqId := regexp.Compile(`\(request id:.*?\)`)
	if errReqId != nil {
		logger.SysError(fmt.Sprintf("errReqId==>failed to compile regex: %s", errReqId.Error()))
	}
	err.Error.Message = reReqId.ReplaceAllString(err.Error.Message, "")
	err.Error.Message = helper.MessageWithRequestId(err.Error.Message, c.GetString(helper.RequestIdKey))

	// URL屏蔽逻辑
	urlRegex := `((https?|ftp):\/\/)?[\w.-]+\.[a-zA-Z]{2,}(:\d+)?(\/[\w.-]*)*`
	re, errReg := regexp.Compile(urlRegex)
	if errReg != nil {
		logger.SysError(fmt.Sprintf("errReg==>failed to compile regex: %s", errReg.Error()))
	}
	sanitizedMessage := re.ReplaceAllString(err.Error.Message, "url")

	// IP地址屏蔽逻辑
	ipPattern := `(?:\b(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b|\b(?:[A-Fa-f0-9]{1,4}:){7}[A-Fa-f0-9]{1,4}\b)(:\d+)?`
	ipr, _ := regexp.Compile(ipPattern)
	sanitizedMessage = ipr.ReplaceAllString(sanitizedMessage, "ip:port")

	if sanitizedMessage != err.Error.Message {
		logger.SysError(fmt.Sprintf("报错信息中包含URL或IP,原始报错信息为: %s", err.Error.Message))
	}
	err.Error.Message = sanitizedMessage

	if err.LocalizedMessage == "" {
		// 判断err.Code这个字段是否是string类型 err.Code.(string)
		if str, ok := err.Code.(string); ok {
			// 是字符串类型
			err.LocalizedMessage = common.GetErrorMessage(str, "zh")
		} else {
			err.LocalizedMessage = common.GetErrorMessage(err.Type, "zh")
		}
	}
	c.JSON(err.StatusCode, gin.H{
		"error": err.Error,
	})

	userId := c.GetInt(ctxkey.Id)
	if model.ShouldLogDownstreamError(userId) {
		logMessage := &model.ErrorLogMessage{
			Description: "抛出到下游错误",
			DownstreamError: &model.ErrorInfo{
				StatusCode: err.StatusCode,
				Error: model.ErrorDetailInfo{
					Message: err.Error.Message,
					Type:    err.Error.Type,
					Code:    err.Error.Code,
				},
			},
			OriginalError: &model.ErrorInfo{
				StatusCode: originalError.StatusCode,
				Error: model.ErrorDetailInfo{
					Message: originalError.Error.Message,
					Type:    originalError.Error.Type,
					Code:    originalError.Error.Code,
				},
			},
			RequestParams: prompt,
		}
		logJson, _ := json.Marshal(logMessage)
		model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, string(logJson), prompt)
	}

	// 记录重试超过最大限度的错误
	maxRetryLogMessage := &model.ErrorLogMessage{
		Description: "重试超过最大限度,此异常会抛出给调用者",
		DownstreamError: &model.ErrorInfo{
			StatusCode: err.StatusCode,
			Error: model.ErrorDetailInfo{
				Message: err.Error.Message,
				Type:    err.Error.Type,
				Code:    err.Error.Code,
			},
		},
		OriginalError: &model.ErrorInfo{
			StatusCode: originalError.StatusCode,
			Error: model.ErrorDetailInfo{
				Message: originalError.Error.Message,
				Type:    originalError.Error.Type,
				Code:    originalError.Error.Code,
			},
		},
		RequestParams: prompt,
	}
	maxRetryLogJson, _ := json.Marshal(maxRetryLogMessage)
	model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeSystemErr, string(maxRetryLogJson), prompt)
}

func relayByDifferentMode(c *gin.Context) (*relayModel.ErrorWithStatusCode, bool, meta.Meta) {
	relayMode := constant.Path2RelayMode(c.Request.URL.Path, c)
	var err *relayModel.ErrorWithStatusCode
	var isTimeout bool
	var relayMeta meta.Meta

	switch relayMode {
	case relaymode.ImagesGenerations, relaymode.ImagesEdits:
		err, isTimeout, relayMeta = controller.RelayImageHelper(c, relayMode)
	// case constant.RelayModeVideoGenerations:
	//	err, isTimeout, relayMeta = controller.RelayVideoHelper(c, relayMode) // 已被new-api风格的RelayTask替代
	case relaymode.AudioSpeech:
		fallthrough
	case relaymode.AudioTranslation:
		fallthrough
	case relaymode.AudioTranscription:
		err = controller.RelayAudioHelper(c, relayMode)
	case relaymode.SearchSerper:
		err, isTimeout, relayMeta = relaySearchSerperHelper(c, relayMode)
	case constant.RelayModeOpenaiNetChatCompletions:
		err, isTimeout, relayMeta = controller.RelayTextHelper(c)
	case relaymode.GPTGodChatCompletions:
		err = relayGPTGodTextHelper(c, relayMode)
	case relaymode.VisionPreviewChatCompletions:
		err = controller.RelayVisionPreviewTextHelper(c, relayMode)
	case relaymode.OpenaiMJChatCompletions:
		err = relayOpenaiMJTextHelper(c, relayMode)
	case constant.RelayModeOpenaiSunoChatCompletions:
		err = relayOpenaiSunoTextHelper(c, relayMode)
	case relaymode.OpenaiDalleChatCompletions:
		err = relayOpenaiDalleTextHelper(c, relayMode)
	case relaymode.OpenaiSoraChatCompletions:
		err = relayOpenaiSoraChatTextHelper(c, relayMode)
	case constant.RelayModeOpenaiImageChatCompletions:
		err = relayOpenaiImageTextHelper(c, relayMode)
	case relaymode.OpenaiLobeChatCompletions:
		err = relayOpenaiLobeTextHelper(c, relayMode)
	case constant.RelayModeOpenaiSerperSearchCompletions:
		err = relayOpenaiSerperSearchTextHelper(c, relayMode)
	case constant.RelayModeResponses:
		err, isTimeout, relayMeta = controller.RelayResponsesHelper(c)
	case constant.RelayModeGetResponse:
		err, isTimeout, relayMeta = controller.RelayGetResponseHelper(c)
	default:
		err, isTimeout, relayMeta = controller.RelayTextHelper(c)
	}

	// 如果有错误，设置错误码
	if err != nil {
		errorCode := ""
		// 判断err.Code这个字段是否是string类型
		if str, ok := err.Error.Code.(string); ok {
			// 是字符串类型
			errorCode = str
		} else {
			// 不是字符串类型，使用 err.Error.Type
			errorCode = err.Error.Type
		}

		// 设置到 gin.Context
		c.Set(ctxkey.ErrorCode, errorCode)

		// 设置到 meta
		relayMeta.ErrorCode = errorCode
	}

	return err, isTimeout, relayMeta
}

// RelayGeminiOfficial 处理Gemini官方格式的请求
func RelayGeminiOfficial(c *gin.Context) {
	ctx := c.Request.Context()
	if config.DebugEnabled {
		requestBody, _ := common.GetRequestBody(c)
		logger.Debugf(ctx, "gemini official request body: %s", string(requestBody))
	}

	// 从URL路径中提取模型名称和操作
	modelParam := c.Param("model")
	if modelParam == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": gin.H{
				"message": "model parameter is required",
				"type":    "invalid_request_error",
			},
		})
		return
	}

	// 解析模型名称和操作 (例如: "gemini-2.5-pro:generateContent")
	parts := strings.Split(modelParam, ":")
	if len(parts) != 2 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": gin.H{
				"message": "invalid model format, expected 'model:action'",
				"type":    "invalid_request_error",
			},
		})
		return
	}

	modelName := parts[0]
	action := parts[1]

	// 验证操作类型
	var relayMode int
	switch action {
	case "generateContent":
		relayMode = relaymode.ChatCompletions
	case "streamGenerateContent":
		relayMode = relaymode.ChatCompletions
		// 设置流式标志 - 这个会在parseGeminiOfficialRequest中设置
	case "batchEmbedContents":
		relayMode = relaymode.Embeddings
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"error": gin.H{
				"message": fmt.Sprintf("unsupported action: %s", action),
				"type":    "invalid_request_error",
			},
		})
		return
	}

	// 将Gemini格式的请求转换为OpenAI格式
	geminiRequest, err := parseGeminiOfficialRequest(c, action)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": gin.H{
				"message": err.Error(),
				"type":    "invalid_request_error",
			},
		})
		return
	}

	// 设置模型名称
	geminiRequest.Model = modelName

	// 将转换后的请求设置到context中
	c.Set(ctxkey.RequestModel, modelName)
	c.Set("converted_request", geminiRequest)

	// 设置relay模式
	c.Set("relay_mode", relayMode)

	// 调用标准的Relay处理流程
	Relay(c)
}

// parseGeminiOfficialRequest 解析Gemini官方格式的请求并转换为OpenAI格式
func parseGeminiOfficialRequest(c *gin.Context, action string) (*relayModel.GeneralOpenAIRequest, error) {
	// 读取原始请求体
	body, err := common.GetRequestBody(c)
	if err != nil {
		return nil, fmt.Errorf("failed to read request body: %w", err)
	}

	// 解析Gemini格式的请求
	var geminiReq map[string]interface{}
	if err := json.Unmarshal(body, &geminiReq); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}

	// 创建OpenAI格式的请求
	openaiReq := &relayModel.GeneralOpenAIRequest{
		Messages: []relayModel.Message{},
		Stream:   action == "streamGenerateContent", // 根据action设置流式标志
	}

	// 转换contents字段
	if contents, ok := geminiReq["contents"].([]interface{}); ok {
		for _, content := range contents {
			if contentMap, ok := content.(map[string]interface{}); ok {
				message := relayModel.Message{}

				// 设置角色
				if role, ok := contentMap["role"].(string); ok {
					message.Role = role
				} else {
					message.Role = "user" // 默认角色
				}

				// 转换parts字段
				if parts, ok := contentMap["parts"].([]interface{}); ok {
					var textParts []string
					for _, part := range parts {
						if partMap, ok := part.(map[string]interface{}); ok {
							if text, ok := partMap["text"].(string); ok {
								textParts = append(textParts, text)
							}
							// TODO: 处理其他类型的part (如图片、音频等)
						}
					}
					if len(textParts) > 0 {
						message.Content = strings.Join(textParts, "\n")
					}
				}

				openaiReq.Messages = append(openaiReq.Messages, message)
			}
		}
	}

	// 转换generationConfig字段
	if genConfig, ok := geminiReq["generationConfig"].(map[string]interface{}); ok {
		if maxTokens, ok := genConfig["maxOutputTokens"].(float64); ok {
			openaiReq.MaxTokens = int(maxTokens)
		}
		if temperature, ok := genConfig["temperature"].(float64); ok {
			openaiReq.Temperature = &temperature
		}
		if topP, ok := genConfig["topP"].(float64); ok {
			openaiReq.TopP = &topP
		}
	}

	return openaiReq, nil
}

func doReopenChannelIfNecessary(ctx *gin.Context, channelId int, channelName string, retryInterval int, requestBody string, requestErr *relayModel.ErrorWithStatusCode) {
	channel, err2 := model.GetChannelById(channelId, true)
	if err2 != nil {
		logger.SysError(fmt.Sprintf("failed to get channel: %s", err2.Error()))
		return
	}
	userId := ctx.GetInt("id")
	if userId == 0 {
		userId = -1
	}
	content := fmt.Sprintf("用户id[%d] 通道「%s」（#%d）已被禁用，原因：%s, status_code:%d, OpenAIError.Type:%s,OpenAIError.Code:%s", userId, channelName, channelId, requestErr.Message, requestErr.StatusCode, requestErr.Error.Type, requestErr.Error.Code)
	_, loaded := revivesAfterFailureMap.LoadOrStore(channelId, true)
	if loaded {
		model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemInfo, fmt.Sprintf("当前channel已经存在,不再创建goroutine:%s;最开始导致失败的信息:%s", channelName, content), requestBody)
		return
	}
	if retryInterval <= 0 {
		retryInterval = 300
	}
	helper.SafeGoroutine(func() {
		defer func() {
			revivesAfterFailureMap.Delete(channelId)
			logger.SysLog(fmt.Sprintf("goruntine 删除channel:%s", channelName))
		}()
		cnt := 0
		for {
			if cnt > config.ReviveRetryTimes {
				model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemErr, fmt.Sprintf("第%d次定时轮询已超最大次数==>数据状态修改失败:%s;最开始导致失败的信息:%s", cnt, channelName, content), requestBody)
				channel.Status = common.ChannelStatusMaxRetriesExceeded //停用（自动禁用后达到最大重试次数时设为此状态）
				err := channel.Update()
				if err != nil {
					model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemErr, fmt.Sprintf("修改渠道状态失败==>第%d次定时轮询已超最大次数==>数据状态修改失败:%s;最开始导致失败的信息:%s", cnt, channelName, err.Error()), requestBody)
				}
				return
			}
			// 隔一段时间轮询一次通道是否复活
			time.Sleep(time.Duration(retryInterval) * time.Second)
			cnt++
			model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemInfo, fmt.Sprintf("第%d次定时轮询==>[%d]渠道%s测试通过,重新启用!;最开始导致失败的信息:%s", cnt, channel.Id, channelName, content), requestBody)
			// 校验当前渠道是否为自动禁用状态,如果不是,则有可能被手动禁用或者是其他状态,一律不重试
			channel, err2 = model.GetChannelById(channelId, true)
			if err2 != nil {
				model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemErr, fmt.Sprintf("failed to get channel: %s;最开始导致失败的信息:%s", err2.Error(), content), requestBody)
				return
			}
			if channel.Status != common.ChannelStatusAutoDisabled {
				// 如果当前状态不是自动禁用,则不再轮询,关闭goroutine
				model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemInfo, fmt.Sprintf("第%d次定时轮询==>[%d]该渠道%s状态已经不是[自动禁用];最开始导致失败的信息:%s", cnt, channel.Id, channelName, content), requestBody)
				return
			}
			// 构造测试请求
			testRequest := buildTestRequest("")
			err3, aiError, _, _ := testChannel(channel, testRequest, false)
			if err3 != nil {
				model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemErr, fmt.Sprintf("err3==>第[%d]次定时轮询需要启动的任务[%s]failed to test channel: %s;最开始导致失败的信息:%s", cnt, channelName, err3.Error(), content), requestBody)
				// Limit: 200 / day 则当天不复活了,等待手动复活."This organization has been disabled"是 claude3 的错误，此时也不再轮询
				if strings.Contains(err3.Error(), "Limit: 200 / day") || strings.Contains(err3.Error(), "This organization has been disabled") {
					model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemErr, fmt.Sprintf("第%d次定时轮询[%s]==>数据状态修改失败:达到 RPD 或组织封禁，不再轮询,需手动复活。最开始导致失败的信息:%s", cnt, channelName, content), requestBody)
					channel.Status = common.ChannelStatusMaxRetriesExceeded
					err := channel.Update()
					if err != nil {
						model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemErr, fmt.Sprintf("修改渠道状态失败==>第[%d]次定时轮询需要启动的任务[%s]failed to test channel: %s;最开始导致失败的信息:%s", cnt, channelName, err.Error(), content), requestBody)
					}
					return
				}
				continue
			}
			if aiError != nil {
				model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemErr, fmt.Sprintf("aiError==>第[%d]次定时轮询需要启动的任务[%s]failed to test channel: %s;最开始导致失败的信息:%s", cnt, channelName, aiError.Message, content), requestBody)
				continue
			}

			channel.Status = common.ChannelStatusEnabled
			err := channel.Update()
			if err != nil {
				model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemErr, fmt.Sprintf("dbError==>第[%d]次定时轮询需要启动的任务[%s]failed to test channel: %s;最开始导致失败的信息:%s", cnt, channelName, err.Error(), content), requestBody)
				return
			}
			model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemInfo, fmt.Sprintf("第%d次定时轮询==>[%d]渠道%s测试通过,重新启用!;最开始导致失败的信息:%s", cnt, channel.Id, channelName, content), requestBody)
			return
		}
	})
	return
}

func doReopenAbilityIfNecessary(ctx *gin.Context, channelId int, channelName string, retryInterval int, requestBody string, requestErr *relayModel.ErrorWithStatusCode) {
	ability, err := model.GetFirstAbilityByChannelIdAndModel(channelId, ctx.GetString("request_model"))
	if err != nil {
		logger.SysError(fmt.Sprintf("failed to get ability: %s", err.Error()))
		return
	}

	userId := ctx.GetInt("id")
	if userId == 0 {
		userId = -1
	}
	requestModel := ctx.GetString("request_model")
	content := fmt.Sprintf("用户id[%d] 通道「%s」（#%d）的能力[%s]已被禁用，原因：%s, status_code:%d, OpenAIError.Type:%s,OpenAIError.Code:%s", userId, channelName, channelId, requestModel, requestErr.Message, requestErr.StatusCode, requestErr.Error.Type, requestErr.Error.Code)

	abilityKey := fmt.Sprintf("%d_%s", channelId, requestModel)
	_, loaded := revivesAbilityAfterFailureMap.LoadOrStore(abilityKey, true)
	if loaded {
		model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemInfo, fmt.Sprintf("当前channel的ability已经存在,不再创建goroutine:%s_%s", channelName, requestModel), requestBody)
		return
	}

	if retryInterval <= 0 {
		retryInterval = 300
	}

	helper.SafeGoroutine(func() {
		defer func() {
			revivesAbilityAfterFailureMap.Delete(abilityKey)
			logger.SysLog(fmt.Sprintf("goroutine 删除channel的ability:%s_%s", channelName, requestModel))
		}()

		cnt := 0
		for {
			if cnt > config.ReviveRetryTimes {
				model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemErr, fmt.Sprintf("第%d次定时轮询已超最大次数==>数据状态修改失败:%s_%s;最开始导致失败的信息:%s", cnt, channelName, requestModel, content), requestBody)
				return
			}

			time.Sleep(time.Duration(retryInterval) * time.Second)
			cnt++

			model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemInfo, fmt.Sprintf("第%d次定时轮询==>[%d]渠道%s的能力[%s]测试通过,重新启用!;最开始导致失败的信息:%s", cnt, channelId, channelName, requestModel, content), requestBody)

			ability, err = model.GetFirstAbilityByChannelIdAndModel(channelId, requestModel)
			if err != nil {
				model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemErr, fmt.Sprintf("failed to get ability: %s;最开始导致失败的信息:%s", err.Error(), content), requestBody)
				return
			}

			if *ability.Enabled {
				model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemInfo, fmt.Sprintf("第%d次定时轮询==>[%d]该渠道%s的能力[%s]状态已经是[启用],无需重启,轮询结束;最开始导致失败的信息:%s", cnt, channelId, channelName, requestModel, content), requestBody)
				return
			}

			channel, err2 := model.GetChannelById(channelId, true)
			if err2 != nil {
				model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemErr, fmt.Sprintf("failed to get channel: %s;最开始导致失败的信息:%s", err2.Error(), content), requestBody)
				return
			}

			// 判断当前渠道是否是手动禁用状态 如果是 直接返回 不需要重启了 关闭goroutine
			if channel.Status == common.ChannelStatusManuallyDisabled {
				model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemInfo, fmt.Sprintf("第%d次定时轮询==>[%d]该渠道%s状态已经是[手动禁用]无需重启,轮询结束;最开始导致失败的信息:%s", cnt, channelId, channelName, content), requestBody)
				return
			}

			// 构造测试请求
			testRequest := buildTestRequest(requestModel)
			err3, aiError, _, _ := testChannel(channel, testRequest, true)
			if err3 != nil || aiError != nil {
				model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemErr, fmt.Sprintf("err3==>第[%d]次定时轮询需要启动的任务[%s_%s]failed to test channel: %s;最开始导致失败的信息:%s", cnt, channelName, requestModel, err3.Error(), content), requestBody)
				continue
			}

			ability.Enabled = &[]bool{true}[0]
			err = model.UpdateChannelAbilityStatusByIdReturnErr(ability.ChannelId, requestModel, common.ChannelStatusEnabled)
			if err != nil {
				model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemErr, fmt.Sprintf("dbError==>第[%d]次定时轮询需要启动的任务[%s_%s]failed to update ability: %s;最开始导致失败的信息:%s", cnt, channelName, requestModel, err.Error(), content), requestBody)
				return
			}

			model.RecordSysLogToDBAndFileByGinContext(ctx, model.LogTypeSystemInfo, fmt.Sprintf("第%d次定时轮询==>[%d]渠道%s的能力[%s]测试通过,重新启用!;最开始导致失败的信息:%s", cnt, channelId, channelName, requestModel, content), requestBody)
			return
		}
	})
}

func shouldRetry(c *gin.Context, statusCode int) bool {
	if _, ok := c.Get(ctxkey.SpecificChannelId); ok {
		return false
	}
	if statusCode == http.StatusTooManyRequests {
		return true
	}
	if statusCode/100 == 5 {
		return true
	}
	if statusCode == http.StatusBadRequest {
		return false
	}
	if statusCode/100 == 2 {
		return false
	}
	return true
}

func processChannelRelayError(ctx context.Context, channelId int, channelName string, err *relayModel.ErrorWithStatusCode) {
	logger.Errorf(ctx, "relay error (channel #%d): %s", channelId, err.Message)
	// https://platform.openai.com/docs/guides/error-codes/api-errors
	if monitor.ShouldDisableChannel(&err.Error, err.StatusCode) {
		disableChannel(ctx, channelId, channelName, err.Message)
	}
}

func RelayNotImplemented(c *gin.Context) {
	err := relayModel.Error{
		Message: "API not implemented",
		Type:    "shell_api_error",
		Param:   "",
		Code:    "api_not_implemented",
	}
	userId := c.GetInt(ctxkey.Id)
	if model.ShouldLogDownstreamError(userId) {
		logMessage := &model.ErrorLogMessage{
			Description: "抛出到下游错误API not implemented",
			DownstreamError: &model.ErrorInfo{
				StatusCode: http.StatusNotImplemented,
				Error: model.ErrorDetailInfo{
					Message: err.Message,
					Type:    err.Type,
					Code:    err.Code,
				},
			},
			OriginalError: &model.ErrorInfo{
				StatusCode: http.StatusNotImplemented,
				Error: model.ErrorDetailInfo{
					Message: err.Message,
					Type:    err.Type,
					Code:    err.Code,
				},
			},
			RequestParams: c.GetString("detail_prompt"),
		}
		logJson, _ := json.Marshal(logMessage)
		model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, string(logJson), c.GetString("detail_prompt"))

	}
	c.JSON(http.StatusNotImplemented, gin.H{
		"error": err,
	})
}

func RelayNotFound(c *gin.Context) {
	err := relayModel.Error{
		Message: fmt.Sprintf("Invalid URL (%s %s)", c.Request.Method, c.Request.URL.Path),
		Type:    "invalid_request_error",
		Param:   "",
		Code:    "",
	}
	userId := c.GetInt(ctxkey.Id)
	if model.ShouldLogDownstreamError(userId) {
		logMessage := &model.ErrorLogMessage{
			Description: "抛出到下游错误Invalid URL",
			DownstreamError: &model.ErrorInfo{
				StatusCode: http.StatusNotFound,
				Error: model.ErrorDetailInfo{
					Message: err.Message,
					Type:    err.Type,
					Code:    err.Code,
				},
			},
			OriginalError: &model.ErrorInfo{
				StatusCode: http.StatusNotFound,
				Error: model.ErrorDetailInfo{
					Message: err.Message,
					Type:    err.Type,
					Code:    err.Code,
				},
			},
			RequestParams: c.GetString("detail_prompt"),
		}
		logJson, _ := json.Marshal(logMessage)
		model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, string(logJson), c.GetString("detail_prompt"))
	}
	c.JSON(http.StatusNotFound, gin.H{
		"error": err,
	})
}

func HandleDisableChannelReturnError(c *gin.Context, err *relayModel.ErrorWithStatusCode, channelId int, channelName string, retryInterval int, undeadModeEnabled bool, requestModel string, bodyCopy string) error {
	// 判断是否需要强制抛出错误
	if monitor.IsRelayErrForceThrowError(err, requestModel) {
		return fmt.Errorf("force throw error")
	}

	// 首先检查是否应该禁用整个渠道
	if monitor.ShouldDisableEntireChannel(err) {
		disableChannelErr := DisableChannelByRequestBodyAndErr(c, common.ChannelStatusMaxRetriesExceeded, channelId, channelName, fmt.Sprintf("关键词命中禁用整个渠道: %s", err.Message), string(bodyCopy), err)
		// 账号封禁类错误不需要重启渠道，移除重启调用
		return disableChannelErr
	}

	if config.ChannelAbilityDisableEnabled {
		if monitor.IsRelayErrForceRetry(err, requestModel) || monitor.IsSpecialErrorOnlyNeedRetry(c, retryInterval, undeadModeEnabled, err) {
			// 什么也不做,不需要禁用
			return nil
		} else if monitor.IsCustomCircuitBreakerError(c, err) || monitor.ShouldTemporarilyDisableChannel(c, &err.Error, err.StatusCode) {
			disableChannelErr := DisableChannelAbilityByRequestBodyAndErr(c, channelId, channelName, err.Message, string(bodyCopy), err)
			if disableChannelErr == nil {
				doReopenAbilityIfNecessary(c, channelId, channelName, retryInterval, string(bodyCopy), err)
			}
			return disableChannelErr
		} else if monitor.IsCustomDisableChannelError(c, err) || monitor.ShouldDisableChannel(&err.Error, err.StatusCode) {
			return DisableChannelAbilityByRequestBodyAndErr(c, channelId, channelName, err.Message, string(bodyCopy), err)
		}
	} else {
		if monitor.IsRelayErrForceRetry(err, requestModel) || monitor.IsSpecialErrorOnlyNeedRetry(c, retryInterval, undeadModeEnabled, err) {
			// 什么也不做,不需要禁用
			return nil
		} else if monitor.IsCustomCircuitBreakerError(c, err) || monitor.ShouldTemporarilyDisableChannel(c, &err.Error, err.StatusCode) {
			disableChannelErr := DisableChannelByRequestBodyAndErr(c, common.ChannelStatusAutoDisabled, channelId, channelName, err.Message, string(bodyCopy), err)
			if disableChannelErr == nil {
				doReopenChannelIfNecessary(c, channelId, channelName, retryInterval, string(bodyCopy), err)
			}
			return disableChannelErr
		} else if monitor.IsCustomDisableChannelError(c, err) || monitor.ShouldDisableChannel(&err.Error, err.StatusCode) {
			return DisableChannelByRequestBodyAndErr(c, common.ChannelStatusAutoDisabled, channelId, channelName, err.Message, string(bodyCopy), err)
		}
	}
	return nil
}

func HandleTimeoutError(c *gin.Context, channelId int, channelName string, retryInterval int, bodyCopy string) {
	timeoutErr := openai.ErrorWrapper(errors.New("timeout_breaker"), "timeout_breaker", http.StatusGatewayTimeout)
	if config.ChannelAbilityDisableEnabled {
		dcbrErr := DisableChannelAbilityByRequestBodyAndErr(c, channelId, channelName, "timeout_breaker", string(bodyCopy), timeoutErr)
		if dcbrErr != nil {
			logger.SysError(fmt.Sprintf("failed to disable channel ability: %s", dcbrErr.Error()))
		}
		doReopenAbilityIfNecessary(c, channelId, channelName, retryInterval, string(bodyCopy), timeoutErr)
	} else {
		dcbrErr := DisableChannelByRequestBodyAndErr(c, common.ChannelStatusAutoDisabled, channelId, channelName, "timeout_breaker", string(bodyCopy), timeoutErr)
		if dcbrErr != nil {
			logger.SysError(fmt.Sprintf("failed to disable channel: %s", dcbrErr.Error()))
		}
		doReopenChannelIfNecessary(c, channelId, channelName, retryInterval, string(bodyCopy), timeoutErr)
	}

}

// handleSpecialError 统一处理特殊错误,包括日志记录和响应
func handleSpecialError(c *gin.Context, err *relayModel.ErrorWithStatusCode, description string, requestParams string) {
	// 设置本地化错误消息
	if err.Error.LocalizedMessage == "" {
		if str, ok := err.Error.Code.(string); ok {
			err.Error.LocalizedMessage = common.GetErrorMessage(str, "zh")
		} else {
			err.Error.LocalizedMessage = common.GetErrorMessage(err.Error.Type, "zh")
		}
	}

	// 记录错误日志
	userId := c.GetInt(ctxkey.Id)
	if model.ShouldLogDownstreamError(userId) {
		logMessage := &model.ErrorLogMessage{
			Description: description,
			DownstreamError: &model.ErrorInfo{
				StatusCode: err.StatusCode,
				Error: model.ErrorDetailInfo{
					Message: err.Error.Message,
					Type:    err.Error.Type,
					Code:    err.Error.Code,
				},
			},
			OriginalError: &model.ErrorInfo{
				StatusCode: err.StatusCode,
				Error: model.ErrorDetailInfo{
					Message: err.Error.Message,
					Type:    err.Error.Type,
					Code:    err.Error.Code,
				},
			},
			RequestParams: requestParams,
		}
		c.Set(ctxkey.ErrorCode, err.Error.Code)
		logJson, _ := json.Marshal(logMessage)
		model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, string(logJson), requestParams)
	}

	// 返回错误响应
	c.JSON(err.StatusCode, gin.H{
		"error": err.Error,
	})
}
