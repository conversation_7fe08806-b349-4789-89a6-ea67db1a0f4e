# Gemini 官方格式 API 支持

本系统现在支持 Google Gemini 官方格式的 API 请求，允许用户直接使用 Google 官方文档中的请求格式。

## 支持的路径

### v1beta 版本 (推荐)
- `POST /v1beta/models/{model}:generateContent` - 生成内容
- `POST /v1beta/models/{model}:streamGenerateContent` - 流式生成内容  
- `POST /v1beta/models/{model}:batchEmbedContents` - 批量嵌入
- `GET /v1beta/models` - 列出可用模型
- `GET /v1beta/models/{model}` - 获取特定模型信息

### v1 版本 (兼容性)
- `POST /v1/models/{model}:generateContent` - 生成内容

## 请求格式

### 基本聊天请求
```bash
curl -X POST "https://your-api-domain.com/v1beta/models/gemini-2.5-pro:generateContent" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "Hello, how are you?"
          }
        ]
      }
    ],
    "generationConfig": {
      "maxOutputTokens": 100,
      "temperature": 0.7,
      "topP": 0.9
    }
  }'
```

### 流式请求
```bash
curl -X POST "https://your-api-domain.com/v1beta/models/gemini-2.5-pro:streamGenerateContent" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "Tell me a story"
          }
        ]
      }
    ]
  }'
```

## 支持的模型

所有在系统中配置的 Gemini 模型都支持官方格式，包括但不限于：
- `gemini-2.5-pro`
- `gemini-2.0-flash-exp`
- `gemini-1.5-pro`
- `gemini-1.5-flash`

## 请求字段映射

系统会自动将 Gemini 官方格式转换为内部的 OpenAI 兼容格式：

| Gemini 字段 | OpenAI 字段 | 说明 |
|------------|------------|------|
| `contents[].role` | `messages[].role` | 消息角色 |
| `contents[].parts[].text` | `messages[].content` | 消息内容 |
| `generationConfig.maxOutputTokens` | `max_tokens` | 最大输出token数 |
| `generationConfig.temperature` | `temperature` | 温度参数 |
| `generationConfig.topP` | `top_p` | Top-P 参数 |

## 环境变量配置

确保设置了正确的 Gemini 版本：
```bash
export GEMINI_VERSION=v1beta
```

## 错误处理

系统会返回标准的错误格式：
```json
{
  "error": {
    "message": "错误描述",
    "type": "error_type",
    "code": "error_code"
  }
}
```

## 测试

使用提供的测试脚本验证功能：
```bash
chmod +x test_gemini_official.sh
./test_gemini_official.sh
```

## 注意事项

1. **认证**: 使用标准的 Bearer Token 认证
2. **内容类型**: 请求必须使用 `application/json`
3. **模型格式**: 模型参数必须包含操作，格式为 `model:action`
4. **兼容性**: 系统同时支持原有的 OpenAI 格式和新的 Gemini 官方格式

## 实现细节

- 路由处理: `router/relay.go` 中的 `SetGeminiOfficialRouter` 函数
- 请求转换: `controller/relay.go` 中的 `RelayGeminiOfficial` 和 `parseGeminiOfficialRequest` 函数
- 后端适配: 使用现有的 Gemini 适配器进行实际的 API 调用

这个实现确保了与 Google 官方 Gemini API 文档的完全兼容性，同时保持了系统的统一性和稳定性。
