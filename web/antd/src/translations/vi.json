{"message": {"copyModelSuccess": "Đã sao chép tên mô hình vào clipboard!", "copyFailed": "<PERSON><PERSON> chép thất b<PERSON>i, vui lòng sao chép thủ công", "logoutSuccess": "<PERSON><PERSON><PERSON> xuất thành công", "loginSuccess": {"default": "<PERSON><PERSON><PERSON> nh<PERSON>p thành công", "welcomeBack": "<PERSON><PERSON><PERSON> mừng trở lại"}, "removeLocalStorage": {"confirm": "Có xóa bộ nhớ cache cục bộ không?", "success": "Xóa bộ nhớ cache cục bộ thành công"}, "loadData": {"error": "<PERSON><PERSON><PERSON> dữ liệu {{name}} thất b<PERSON>i"}, "noNotice": "<PERSON><PERSON>n tại không có nội dung thông báo", "verification": {"turnstileChecking": "Turnstile đang kiểm tra môi trường người dùng!", "pleaseWait": "<PERSON><PERSON> lòng thử lại sau"}, "clipboard": {"inviteCodeDetected": "<PERSON><PERSON><PERSON> hiện mã mời, đã tự động điền vào!", "clickToCopy": "<PERSON><PERSON><PERSON><PERSON> để sao chép", "copySuccess": "<PERSON><PERSON> chép thành công"}}, "common": {"yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "copyAll": "<PERSON><PERSON> ch<PERSON>p tất cả", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "more": "<PERSON><PERSON><PERSON><PERSON>", "unlimited": "<PERSON><PERSON><PERSON>ng gi<PERSON>i hạn", "enabled": "<PERSON><PERSON><PERSON>", "disabled": "Tắt", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "create": "Tạo", "usd": "USD", "day": "{{count}} ngày", "day_plural": "{{count}} ngày", "days": "ng<PERSON>y", "seconds": "giây", "times": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "bind": "<PERSON><PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "loading": "<PERSON><PERSON> tả<PERSON>...", "copyFailed": "<PERSON><PERSON> ch<PERSON>p thất b<PERSON>i", "people": "người", "ok": "OK", "close": "Đ<PERSON><PERSON>", "copied": "Đã sao chép", "expand": "Mở rộng", "collapse": "<PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON><PERSON> có", "remark": "<PERSON><PERSON><PERSON>", "selectPlaceholder": "<PERSON><PERSON> lòng chọn {{name}}", "on": "<PERSON><PERSON><PERSON>", "off": "Tắt", "name": "<PERSON><PERSON><PERSON> danh", "displayName": "<PERSON><PERSON><PERSON> hiển thị", "description": "<PERSON><PERSON>", "ratio": "Tỷ lệ", "unnamed": "<PERSON><PERSON><PERSON> ch<PERSON>a đặt tên", "groups": "Nhóm", "captchaPlaceholder": "<PERSON><PERSON> lòng nhập mã xác thực", "confirm": "<PERSON><PERSON><PERSON>", "permissions": "<PERSON><PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>", "createdTime": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "expiredTime": "<PERSON><PERSON><PERSON><PERSON> gian hế<PERSON> hạn", "search": "<PERSON><PERSON><PERSON>", "reset": "Đặt lại", "refresh": "<PERSON><PERSON><PERSON>", "pagination": {"total": "Từ {{start}} - {{end}}, tổng cộng {{total}} mục"}}, "currency": {"symbol": "$"}, "floatButton": {"clickToOpen": "<PERSON><PERSON><PERSON><PERSON> để mở liên kết"}, "userRole": {"normal": "<PERSON><PERSON><PERSON><PERSON> dùng thường", "agent": "<PERSON><PERSON><PERSON> lý", "admin": "<PERSON><PERSON><PERSON><PERSON> trị viên", "superAdmin": "<PERSON><PERSON><PERSON> quản trị viên", "loading": "<PERSON><PERSON> tả<PERSON>..."}, "channelStatus": {"enabled": "<PERSON><PERSON> bật", "disabled": "Đã tắt", "waitingRestart": "<PERSON><PERSON> chờ khởi động lại", "waiting": "<PERSON><PERSON> chờ", "autoStoppedTitle": "<PERSON><PERSON><PERSON> đã vượt quá số lần thử lại tối đa hoặc kích hoạt điều kiện tự động dừng", "stopped": "Đã dừng", "partiallyDisabled": "<PERSON><PERSON><PERSON> một ph<PERSON>n", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "reason": "Lý do"}, "channelBillingTypes": {"payAsYouGo": "<PERSON><PERSON> toán theo sử dụng", "payPerRequest": "<PERSON><PERSON> to<PERSON> theo yêu c<PERSON>u", "unknown": "<PERSON><PERSON><PERSON><PERSON> thức không xác đ<PERSON>nh"}, "tokenStatus": {"normal": "<PERSON><PERSON><PERSON>", "disabled": "Đã tắt", "expired": "<PERSON><PERSON> hết hạn", "exhausted": "Đã cạn ki<PERSON>t", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>"}, "userStatus": {"normal": "<PERSON><PERSON><PERSON>", "banned": "<PERSON><PERSON> cấm", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>"}, "redemptionStatus": {"normal": "<PERSON><PERSON><PERSON>", "disabled": "Đã tắt", "redeemed": "<PERSON><PERSON> đổi", "expired": "<PERSON><PERSON> hết hạn", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>"}, "duration": {"request": "<PERSON><PERSON><PERSON> c<PERSON>", "firstByte": "Byte đầu tiên", "total": "<PERSON><PERSON><PERSON> cộng", "seconds": "giây", "lessThanOneSecond": "<1 giây"}, "streamType": {"stream": "<PERSON><PERSON><PERSON>", "nonStream": "<PERSON><PERSON><PERSON><PERSON> luồng"}, "noSet": {"title": "<PERSON><PERSON><PERSON><PERSON> trị viên chưa thiết lập {{name}}", "name": {"about": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "chat": "<PERSON><PERSON><PERSON>"}}, "buttonText": {"add": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "edit": "Chỉnh sửa", "save": "<PERSON><PERSON><PERSON>", "updateBalance": "<PERSON><PERSON><PERSON> nh<PERSON>t số dư", "test": "<PERSON><PERSON><PERSON> tra", "multiple": "<PERSON><PERSON><PERSON>"}, "channelPage": {"title": "<PERSON><PERSON><PERSON><PERSON> lý k<PERSON>nh"}, "channelStatusCount": {"title": "Th<PERSON>ng kê trạng thái kênh", "summary": "<PERSON><PERSON> bật {{enabled}} | <PERSON><PERSON> tắt {{disabled}} | <PERSON><PERSON> thử lại {{retry}} | Đã dừng {{stopped}} | Tắt một phần {{partial}}", "statusEnabled": "<PERSON><PERSON> bật", "statusDisabled": "Đã tắt", "statusRetry": "<PERSON><PERSON> thử lại", "statusStopped": "Đã dừng", "statusPartially": "<PERSON><PERSON><PERSON> một ph<PERSON>n"}, "header": {"routes": {"status": "<PERSON><PERSON><PERSON><PERSON> thái", "home": "Trang chủ", "chat": "<PERSON><PERSON><PERSON>", "pptGen": "Tạo PPT", "chart": "<PERSON><PERSON><PERSON><PERSON> kê", "agency": "<PERSON><PERSON><PERSON> lý", "channel": "<PERSON><PERSON><PERSON>", "ability": "<PERSON><PERSON><PERSON> n<PERSON>ng kênh", "channelGroup": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "token": "Token", "log": "<PERSON><PERSON><PERSON><PERSON> ký", "logDetail": "<PERSON> ti<PERSON>", "midjourney": "Vẽ tranh", "task": "<PERSON><PERSON><PERSON> v<PERSON> bất đồng bộ", "user": "<PERSON><PERSON><PERSON><PERSON> dùng", "config": "<PERSON><PERSON><PERSON> h<PERSON>nh", "packagePlanAdmin": "<PERSON><PERSON><PERSON> v<PERSON>", "redemption": "<PERSON>ã đổi thưởng", "group": "Nhóm", "query": "<PERSON><PERSON><PERSON> v<PERSON>n", "about": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "agencyJoin": "<PERSON><PERSON> <PERSON>h<PERSON><PERSON> đạ<PERSON> lý", "setting": {"default": "Cài đặt", "operation": "<PERSON>ài đặt vận hành", "system": "<PERSON><PERSON><PERSON> đặt hệ thống", "global": "Cài đặt toàn cục", "advance": "<PERSON>ài đặt tính năng", "sensitive": "<PERSON><PERSON><PERSON> hình từ nh<PERSON>y cảm", "verification": "<PERSON><PERSON><PERSON> hình mã x<PERSON>c thực", "update": "<PERSON><PERSON><PERSON> tra cập nh<PERSON>t"}, "account": {"default": "<PERSON><PERSON><PERSON>", "profile": "Trung tâm cá nhân", "cardTopup": "Đổi mã thẻ", "onlineTopup": "<PERSON><PERSON><PERSON> tiền trự<PERSON> tuyến", "recharge": "<PERSON><PERSON><PERSON> số dư", "balanceTransfer": "<PERSON><PERSON><PERSON><PERSON> số dư", "pricing": "<PERSON><PERSON><PERSON><PERSON> thích phí", "notificationSettings": "Cài đặt thông báo", "packagePlan": {"list": "<PERSON><PERSON> g<PERSON><PERSON> d<PERSON> vụ", "record": "<PERSON><PERSON><PERSON> sử mua hàng"}}, "tools": {"default": "<PERSON><PERSON><PERSON> cụ", "fileUpload": "<PERSON><PERSON><PERSON> lên t<PERSON>", "keyExtraction": "<PERSON><PERSON><PERSON><PERSON> x<PERSON> k<PERSON>", "multiplierCalculator": "<PERSON><PERSON><PERSON> h<PERSON> s<PERSON>", "shortLink": "<PERSON><PERSON><PERSON> liên kết ng<PERSON>n", "testConnection": "<PERSON><PERSON><PERSON> tra truy cập", "customPrompts": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> prompt", "redis": "<PERSON>r<PERSON><PERSON> quan hóa <PERSON>", "ratioCompare": "So sánh tỷ lệ", "serverLog": "<PERSON><PERSON><PERSON><PERSON> xem nhật ký máy chủ"}, "onlineTopupRecord": "<PERSON><PERSON><PERSON> sử nạp tiền", "channelScores": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>nh", "dynamicRouter": "<PERSON><PERSON><PERSON> tuyến động"}, "dropdownMenu": {"profile": "Trung tâm cá nhân", "recharge": "<PERSON><PERSON><PERSON> số dư", "agencyCenter": "Trung tâm đại lý", "checkin": "<PERSON><PERSON><PERSON><PERSON>nh", "darkMode": {"enable": "<PERSON><PERSON> độ tối", "disable": "<PERSON><PERSON> độ sáng"}, "fullScreen": {"default": "<PERSON><PERSON><PERSON><PERSON> toàn màn hình", "enable": "<PERSON><PERSON> độ toàn màn hình", "disable": "<PERSON><PERSON><PERSON><PERSON> toàn màn hình"}, "logout": "<PERSON><PERSON><PERSON> xu<PERSON>"}, "checkin": {"default": "<PERSON><PERSON><PERSON><PERSON>nh", "success": "<PERSON><PERSON><PERSON><PERSON> danh thành công", "failed": "<PERSON><PERSON><PERSON><PERSON> danh thất bại", "verification": "<PERSON>ui lòng hoàn thành xác thực"}, "avatarProps": {"login": "<PERSON><PERSON><PERSON>"}}, "settings": {"public": {"titles": {"default": "Cài đặt công khai"}, "SystemName": "<PERSON><PERSON><PERSON> th<PERSON>ng", "ServerAddress": "Địa chỉ dịch vụ", "TopUpLink": "<PERSON><PERSON><PERSON> kết n<PERSON>p tiền", "ChatLink": "<PERSON><PERSON><PERSON> kết trò chuy<PERSON>n", "Logo": "<PERSON><PERSON> h<PERSON> thống", "HomePageContent": "<PERSON><PERSON>i dung trang chủ", "About": "<PERSON><PERSON><PERSON> dung giới thiệu", "Notice": "<PERSON><PERSON><PERSON> dung thông báo", "Footer": "<PERSON><PERSON><PERSON> dung chân trang", "RegisterInfo": "<PERSON><PERSON><PERSON><PERSON> báo đăng ký", "HeaderScript": "Header t<PERSON>y chỉnh", "SiteDescription": "<PERSON><PERSON> tả trang web", "PrivacyPolicy": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> b<PERSON><PERSON> mật", "ServiceAgreement": "Thỏa thuận dịch vụ", "FloatButton": {"FloatButtonEnabled": "<PERSON><PERSON><PERSON>", "DocumentInfo": "Thông tin tài liệu", "WechatInfo": "Thông tin WeChat", "QqInfo": "Thông tin QQ"}, "CustomThemeConfig": "Chủ đề tùy chỉnh", "AppList": "<PERSON><PERSON><PERSON> kết bạn bè"}}, "home": {"default": {"title": "<PERSON><PERSON>o mừng sử dụng ", "subtitle": "Dựa trên One API phát triển thứ cấp, cung cấp chức năng hoàn thiện hơn", "start": "<PERSON><PERSON><PERSON> đ<PERSON>u sử dụng", "description": {"title": "<PERSON><PERSON><PERSON> n<PERSON>ng mới:", "part1": "<PERSON><PERSON><PERSON> di<PERSON>n người dùng hoàn toàn mới, tiện lợi và nhanh chóng", "part2": "<PERSON><PERSON><PERSON> ưu hóa cơ chế điều phối, hiệ<PERSON> qu<PERSON> và <PERSON>n định", "part3": "<PERSON><PERSON><PERSON> triển cho do<PERSON>h <PERSON>, an toàn và đáng tin cậy", "part4": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h năng nâng cao h<PERSON>n, chờ bạn khám phá"}}}, "dailyUsageChart": {"title": "<PERSON><PERSON><PERSON> hình sử dụng mô hình hàng ngày", "yAxisName": "<PERSON><PERSON><PERSON><PERSON> sử dụng (USD)", "loadingTip": "<PERSON><PERSON><PERSON> hình sử dụng hàng ngày", "fetchError": "Lỗi khi lấy dữ liệu sử dụng hàng ngày:"}, "modelUsageChart": {"title": "<PERSON><PERSON><PERSON> hình sử dụng mô hình", "hourlyTitle": "<PERSON><PERSON><PERSON> hình sử dụng mô hình theo giờ", "dailyTitle": "<PERSON><PERSON><PERSON> hình sử dụng mô hình hàng ngày", "weeklyTitle": "<PERSON><PERSON><PERSON> hình sử dụng mô hình hàng tuần", "monthlyTitle": "<PERSON><PERSON><PERSON> hình sử dụng mô hình hàng tháng"}, "granularity": {"hour": "<PERSON>", "day": "<PERSON><PERSON><PERSON>", "week": "<PERSON><PERSON><PERSON> t<PERSON>", "month": "<PERSON><PERSON><PERSON>g", "all": "<PERSON><PERSON><PERSON> c<PERSON>"}, "abilitiesTable": {"title": "<PERSON><PERSON><PERSON> n<PERSON>ng kênh", "export": "<PERSON><PERSON><PERSON>", "group": "Nhóm", "model": "<PERSON><PERSON>", "channelId": "Số kênh", "enabled": "<PERSON><PERSON> bật", "weight": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "priority": "Ưu tiên", "billingType": "Loại t<PERSON>h phí", "functionCallEnabled": "<PERSON><PERSON><PERSON> h<PERSON>m", "imageSupported": "Hỗ trợ hình <PERSON>nh", "yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "perToken": "<PERSON><PERSON>h phí theo <PERSON>", "perRequest": "<PERSON><PERSON><PERSON> phí theo yêu c<PERSON>u", "noDataToExport": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu để xuất", "exportConfirm": "Bạn có chắc chắn muốn xuất dữ liệu trang hiện tại không?", "exportSuccess": "<PERSON><PERSON><PERSON> thành công", "toggleSuccess": "<PERSON>y<PERSON><PERSON> đổi thành công", "toggleError": "<PERSON>y<PERSON>n đổi thất bại", "selectOrInputGroup": "<PERSON>ọn hoặc nhập nhóm người dùng"}, "logsTable": {"retry": "<PERSON><PERSON><PERSON> lại", "retryChannelList": "<PERSON><PERSON> s<PERSON>ch kênh thử lại", "retryDurations": "<PERSON> tiết thời gian thử lại", "channel": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON><PERSON> gian th<PERSON> hi<PERSON>n", "startTime": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu", "endTime": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc", "retryCount": "<PERSON><PERSON> lần thử lại", "retryDetails": "<PERSON> tiết thử lại", "totalRetryTime": "<PERSON><PERSON><PERSON> thời gian thử lại", "seconds": "giây", "tokenGroup": "Nhóm token", "selectGroup": "<PERSON><PERSON><PERSON>", "dailyModelUsageStats": "Tổng quan dữ liệu g<PERSON>i", "time": "<PERSON><PERSON><PERSON><PERSON> gian", "moreInfo": "<PERSON><PERSON><PERSON><PERSON> thông tin", "ip": "IP", "remoteIp": "IP từ xa", "ipTooltip": "IP: {{ip}}\nIP từ xa: {{remoteIp}}", "requestId": "<PERSON> yêu c<PERSON>u", "username": "<PERSON><PERSON><PERSON> dùng", "userId": "ID người dùng", "tokenName": "Tên token", "token": "Token", "type": "<PERSON><PERSON><PERSON>", "typeUnknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "type充值": "<PERSON><PERSON><PERSON> t<PERSON>", "type消费": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>", "type管理": "<PERSON><PERSON><PERSON><PERSON> lý", "type系统": "<PERSON><PERSON> th<PERSON>", "type邀请": "<PERSON><PERSON><PERSON>", "type提示": "G<PERSON><PERSON> ý", "type警告": "<PERSON><PERSON><PERSON> b<PERSON>o", "type错误": "Lỗi", "type签到": "<PERSON><PERSON><PERSON><PERSON>nh", "type日志": "<PERSON><PERSON><PERSON><PERSON> ký", "type退款": "<PERSON><PERSON><PERSON> ti<PERSON>n", "type邀请奖励金划转": "Chuyển tiền thưởng mời", "type代理奖励": "Thưởng đại lý", "type下游错误": "Lỗi downstream", "type测试渠道": "<PERSON><PERSON><PERSON> tra kênh", "typeRecharge": "<PERSON><PERSON><PERSON> t<PERSON>", "typeConsumption": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>", "typeManagement": "<PERSON><PERSON><PERSON><PERSON> lý", "typeSystem": "<PERSON><PERSON> th<PERSON>", "typeInvitation": "<PERSON><PERSON><PERSON>", "typePrompt": "G<PERSON><PERSON> ý", "typeWarning": "<PERSON><PERSON><PERSON> b<PERSON>o", "typeError": "Lỗi", "typeCheckin": "<PERSON><PERSON><PERSON><PERSON>nh", "typeLog": "<PERSON><PERSON><PERSON><PERSON> ký", "typeRefund": "<PERSON><PERSON><PERSON> ti<PERSON>n", "typeInviteReward": "Chuyển tiền thưởng mời", "typeAgencyBonus": "Thưởng đại lý", "typeDownstreamError": "Lỗi downstream", "typeChannelTest": "<PERSON><PERSON><PERSON> tra kênh", "channelId": "ID kênh", "channelName": "<PERSON><PERSON><PERSON> k<PERSON>", "model": "<PERSON><PERSON>", "modelPlaceholder": "<PERSON><PERSON><PERSON><PERSON>/chọn tên mô hình", "info": "Thông tin", "isStream": "Streaming", "isStreamPlaceholder": "<PERSON>h<PERSON><PERSON>/chọn có <PERSON> không", "prompt": "Prompt", "completion": "Completion", "consumption": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>", "consumptionRange": "<PERSON>ạm vi hạn mức tiêu thụ", "description": "<PERSON><PERSON>", "action": "<PERSON><PERSON>", "details": "<PERSON> ti<PERSON>", "tokenKey": "Khóa token", "requestDuration": "<PERSON><PERSON><PERSON><PERSON> gian yêu c<PERSON>u", "firstByteDuration": "Thời gian byte đầu", "totalDuration": "<PERSON><PERSON><PERSON> thời gian", "lessThanOneSecond": "<1 giây", "modelInvocation": "<PERSON><PERSON><PERSON> mô hình", "modelUsage": "<PERSON><PERSON><PERSON> hình sử dụng mô hình", "totalQuota": "<PERSON><PERSON><PERSON> hạn mức tiêu thụ: {{quota}}", "totalRpm": "<PERSON><PERSON> yêu cầu/phút: {{rpm}}", "totalTpm": "S<PERSON>/phút: {{tpm}}", "totalMpm": "<PERSON><PERSON> tiền/phút: {{mpm}}", "dailyEstimate": "Ước tính tiêu thụ hàng ngày: {{estimate}}", "currentStats": "RPM hiện tại:{{rpm}} TPM hiện tại:{{tpm}} MPM hiện tại:${{mpm}} Ước tính hàng ngày:${{dailyEstimate}}", "statsTooltip": "Chỉ thống kê nhật ký chưa lưu trữ, RPM: số yêu cầu mỗi phút, TPM: số <PERSON> mỗi phút, MPM: số tiền tiêu thụ mỗi phút, ướ<PERSON> t<PERSON>h hàng ngày dựa trên MPM hiện tại", "showAll": "<PERSON><PERSON><PERSON> thị tất cả", "exportConfirm": "<PERSON><PERSON>t nhật ký trang này?", "export": "<PERSON><PERSON><PERSON>", "statsData": "<PERSON><PERSON> liệu thống kê", "today": "<PERSON><PERSON><PERSON> nay", "lastHour": "1 giờ", "last3Hours": "3 giờ", "lastDay": "1 ngày", "last3Days": "3 ngày", "last7Days": "7 ngày", "lastMonth": "1 tháng", "last3Months": "3 tháng", "excludeModels": "Loại trừ mô hình", "selectModelsToExclude": "<PERSON><PERSON><PERSON> mô hình cần loại trừ", "excludeErrorCodes": "Loại trừ mã lỗi", "excludeErrorCodesPlaceholder": "<PERSON><PERSON>n mã lỗi cần loại trừ", "errorCode": "Mã lỗi", "errorCodePlaceholder": "Nh<PERSON><PERSON>/chọn mã lỗi", "timezoneTip": "<PERSON><PERSON><PERSON> giờ hiện tại: {timezone}", "timezoneNote": "<PERSON>hi chú múi giờ", "timezoneDescription": "Dữ liệu thống kê được nhóm theo ngày theo múi giờ hiện tại của bạn. Múi giờ khác nhau có thể dẫn đến khác biệt trong khoảng thời gian nhóm dữ liệu. Nếu cần điều chỉnh, vui lòng đến trung tâm cá nhân để thay đổi cài đặt múi giờ.", "goToProfile": "<PERSON>ến trung tâm cá nhân", "realtimeQuota": "<PERSON><PERSON><PERSON><PERSON> thụ thời gian thực (1 phút)", "viewTotalQuota": "<PERSON><PERSON> tổng tiêu thụ", "viewTotalQuotaTip": "<PERSON><PERSON> tổng số tiền tiêu thụ lịch sử (t<PERSON><PERSON> vấn có thể mất vài gi<PERSON>y)", "loadingTotalQuota": "<PERSON><PERSON> truy vấn tổng số tiền tiêu thụ, vui lòng đợi...", "totalQuotaTitle": "<PERSON><PERSON><PERSON><PERSON> kê tổng tiêu thụ lịch sử", "loadTotalQuotaError": "<PERSON><PERSON><PERSON> tổng số tiền tiêu thụ thất bại", "requestLogs": "<PERSON><PERSON><PERSON><PERSON> ký yêu cầu - {{requestId}}", "noRequestLogs": "<PERSON><PERSON><PERSON> có nhật ký yêu cầu", "metricsExplanation": "Chỉ thống kê nhật ký chưa lưu trữ, RPM: số yêu cầu mỗi phút, TPM: số <PERSON> mỗi phút, MPM: số tiền tiêu thụ mỗi phút, ướ<PERSON> t<PERSON>h hàng ngày dựa trên MPM hiện tại", "autoRefresh": "Tự động làm mới", "autoRefreshTip": "Nh<PERSON><PERSON> để bật/tắt tự động làm mới, khi bật sẽ tự động làm mới dữ liệu theo khoảng thời gian đã chỉ định", "autoRefreshOn": "<PERSON><PERSON> bật tự động làm mới", "autoRefreshOff": "<PERSON><PERSON> tắt tự động làm mới", "refreshInterval": "<PERSON><PERSON><PERSON><PERSON> thời gian làm mới", "stopRefresh": "<PERSON><PERSON><PERSON> làm mới", "secondsWithValue": "{{seconds}} gi<PERSON>y", "minutesWithValue": "{{minutes}} ph<PERSON>t"}, "mjLogs": {"logId": "ID nhật ký", "submitTime": "<PERSON><PERSON><PERSON><PERSON> gian g<PERSON>i", "type": "<PERSON><PERSON><PERSON>", "channelId": "ID kênh", "userId": "ID người dùng", "taskId": "ID tác vụ", "submit": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "progress": "<PERSON><PERSON><PERSON><PERSON> độ", "duration": "<PERSON><PERSON><PERSON><PERSON> gian th<PERSON> hi<PERSON>n", "result": "<PERSON><PERSON><PERSON> qu<PERSON>", "prompt": "Prompt", "promptEn": "PromptEn", "failReason": "Lý do thất bại", "startTime": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu", "endTime": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc", "today": "<PERSON><PERSON><PERSON> nay", "lastHour": "1 giờ", "last3Hours": "3 giờ", "lastDay": "1 ngày", "last3Days": "3 ngày", "last7Days": "7 ngày", "lastMonth": "1 tháng", "last3Months": "3 tháng", "selectTaskType": "<PERSON><PERSON>n lo<PERSON>i tác vụ", "selectSubmitStatus": "<PERSON><PERSON><PERSON> tình trạng gửi", "submitSuccess": "<PERSON><PERSON><PERSON> thành công", "queueing": "<PERSON><PERSON> hàng", "duplicateSubmit": "<PERSON><PERSON><PERSON> trùng lặp", "selectTaskStatus": "<PERSON><PERSON>n trạng thái tác vụ", "success": "<PERSON><PERSON><PERSON><PERSON> công", "waiting": "<PERSON><PERSON> chờ", "queued": "<PERSON><PERSON><PERSON>", "executing": "<PERSON><PERSON> th<PERSON> hi<PERSON>n", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "seconds": "giây", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "viewImage": "<PERSON><PERSON><PERSON><PERSON> để xem", "markdownFormat": "<PERSON><PERSON><PERSON> dạng Markdown", "midjourneyTaskId": "ID tác vụ Midjourney", "copiedAsMarkdown": "Đ<PERSON> sao chép dư<PERSON>i định dạng Markdown", "copyFailed": "<PERSON><PERSON> ch<PERSON>p thất b<PERSON>i", "copiedMidjourneyTaskId": "Đã sao chép ID tác vụ Midjourney", "drawingLogs": "<PERSON><PERSON><PERSON><PERSON> ký vẽ", "onlyUnarchived": "Chỉ thống kê nhật ký chưa đư<PERSON><PERSON> lưu trữ", "imagePreview": "<PERSON><PERSON> t<PERSON><PERSON><PERSON>", "copiedImageUrl": "Đã sao chép địa chỉ hình ảnh", "copy": "Sao chép", "download": "<PERSON><PERSON><PERSON>", "resultImage": "<PERSON><PERSON><PERSON> kết quả", "downloadError": "<PERSON><PERSON><PERSON> xuống hình <PERSON>nh thất bại", "mode": "<PERSON><PERSON> độ", "selectMode": "<PERSON><PERSON><PERSON> ch<PERSON> độ", "relax": "<PERSON><PERSON> độ thư giãn", "fast": "<PERSON><PERSON> độ nhanh", "turbo": "<PERSON><PERSON> độ siêu tốc", "actions": "<PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON>", "tasks": {"title": "Nhiệm vụ bất đồng bộ", "taskId": "ID nhiệm vụ", "platform": "<PERSON><PERSON><PERSON> t<PERSON>", "type": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "progress": "<PERSON><PERSON><PERSON><PERSON> độ", "submitTime": "<PERSON><PERSON><PERSON><PERSON> gian g<PERSON>i", "startTime": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu", "endTime": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc", "duration": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "result": "<PERSON><PERSON><PERSON> qu<PERSON>", "taskIdPlaceholder": "Nhập ID nhiệm vụ", "platformPlaceholder": "<PERSON><PERSON><PERSON> n<PERSON>n tảng", "typePlaceholder": "<PERSON><PERSON><PERSON> lo<PERSON> n<PERSON> vụ", "statusPlaceholder": "<PERSON><PERSON><PERSON> trạng thái", "videoGeneration": "Tạo video", "imageGeneration": "<PERSON><PERSON><PERSON>", "musicGeneration": "Tạo nhạc", "textGeneration": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON> công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "inProgress": "<PERSON><PERSON> th<PERSON> hi<PERSON>n", "submitted": "Đ<PERSON> gửi", "queued": "<PERSON><PERSON> chờ", "notStarted": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu", "viewResult": "<PERSON><PERSON> kế<PERSON> quả", "viewError": "Xem lỗi", "taskDetails": "<PERSON> ti<PERSON><PERSON> n<PERSON> vụ", "errorDetails": "<PERSON> tiết lỗi", "loadError": "Lỗi tải danh s<PERSON>ch nhi<PERSON>m vụ"}, "refreshSuccess": "<PERSON><PERSON><PERSON> mới trạng thái tác vụ thành công", "refreshFailed": "<PERSON><PERSON><PERSON> mới trạng thái tác vụ thất bại", "refreshError": "Lỗi khi làm mới trạng thái tác vụ", "viewVideo": "Xem video", "videoPreview": "<PERSON><PERSON> tr<PERSON> video", "copyVideoUrl": "<PERSON><PERSON> ch<PERSON>p địa chỉ video", "copiedVideoUrl": "<PERSON><PERSON> sao chép địa chỉ video", "downloadVideo": "<PERSON><PERSON><PERSON> video", "videoNotSupported": "Tr<PERSON><PERSON> của bạn không hỗ trợ phát video", "videoUrl": "Địa chỉ video", "videoUrls": "<PERSON><PERSON> s<PERSON>ch địa chỉ video"}, "mjTaskType": {"IMAGINE": "<PERSON><PERSON><PERSON>", "UPSCALE": "<PERSON><PERSON><PERSON> to", "VARIATION": "<PERSON><PERSON><PERSON><PERSON> thể", "REROLL": "<PERSON><PERSON><PERSON> lạ<PERSON>", "DESCRIBE": "<PERSON><PERSON> t<PERSON> h<PERSON>nh <PERSON>nh", "BLEND": "<PERSON><PERSON> tr<PERSON>n", "OUTPAINT": "Mở rộng", "DEFAULT": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>"}, "mjCode": {"submitSuccess": "<PERSON><PERSON><PERSON> thành công", "queueing": "<PERSON><PERSON> hàng", "duplicateSubmit": "<PERSON><PERSON><PERSON> trùng lặp", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>"}, "mjStatus": {"success": "<PERSON><PERSON><PERSON><PERSON> công", "waiting": "<PERSON><PERSON> chờ", "queued": "<PERSON><PERSON><PERSON>", "executing": "<PERSON><PERSON> th<PERSON> hi<PERSON>n", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>"}, "tokensTable": {"title": "<PERSON><PERSON><PERSON><PERSON>", "table": {"title": "<PERSON><PERSON><PERSON><PERSON>", "toolBar": {"add": "<PERSON><PERSON>o <PERSON> mới", "delete": "<PERSON><PERSON><PERSON>", "deleteConfirm": "<PERSON><PERSON> xóa hàng loạt {{count}} token, thao tác này không thể hoàn tác", "export": "<PERSON><PERSON><PERSON>", "exportConfirm": "Xuất token trang hiện tại?"}, "action": "<PERSON><PERSON>"}, "modal": {"title": {"add": "<PERSON><PERSON>o <PERSON> mới", "edit": "Chỉnh sửa <PERSON>"}, "field": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "type": {"default": "<PERSON><PERSON><PERSON><PERSON> thức t<PERSON>h phí", "type1": "<PERSON><PERSON><PERSON> phí theo l<PERSON>", "type2": "<PERSON><PERSON><PERSON> ph<PERSON> theo l<PERSON>n", "type3": "T<PERSON>h phí hỗn hợp", "type4": "Ưu tiên theo l<PERSON>", "type5": "Ưu tiên theo lần"}, "status": "<PERSON><PERSON><PERSON><PERSON> thái", "statusEnabled": "<PERSON><PERSON><PERSON>", "statusDisabled": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "statusExpired": "<PERSON><PERSON><PERSON>", "statusExhausted": "Cạ<PERSON>", "models": "<PERSON><PERSON> hình có sẵn", "usedQuota": "<PERSON><PERSON><PERSON> mức đã sử dụng", "remainQuota": "<PERSON><PERSON><PERSON> mức còn lại", "createdTime": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "expiredTime": "<PERSON><PERSON><PERSON><PERSON> gian hế<PERSON> hạn", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "more": "<PERSON><PERSON><PERSON><PERSON>", "notEnabled": "<PERSON><PERSON><PERSON> b<PERSON>", "unlimited": "<PERSON><PERSON><PERSON>ng gi<PERSON>i hạn", "daysLeft": "<PERSON><PERSON>t hạn sau {{days}} ngày", "expired": "<PERSON><PERSON> hết hạn {{days}} ngày", "userId": "ID người dùng", "key": "Khóa API", "neverExpire": "<PERSON><PERSON><PERSON><PERSON> bao gi<PERSON> hết hạn"}, "delete": {"title": "Xóa", "content": "Bạn có chắc chắn muốn xóa khóa API {{name}} không?"}, "footer": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>"}, "bridge": {"title": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> n<PERSON>h kênh", "placeholder": "<PERSON><PERSON> lòng nhập địa chỉ dịch vụ {{name}} c<PERSON><PERSON> bạn"}, "copy": {"title": "<PERSON><PERSON> ch<PERSON>p thủ công"}}, "dropdown": {"onlineChat": "<PERSON><PERSON><PERSON> chuy<PERSON>n trự<PERSON> tuyến", "disableToken": "<PERSON><PERSON> hi<PERSON> hóa token", "enableToken": "<PERSON><PERSON><PERSON>", "editToken": "Chỉnh sửa token", "requestExample": "<PERSON><PERSON> dụ yêu cầu", "tokenLog": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "shareToken": "Chia sẻ token", "quickIntegration": "<PERSON><PERSON><PERSON> n<PERSON>i một cú nhấp"}, "error": {"fetchModelsFailed": "<PERSON><PERSON><PERSON> mô hình thất bại: {{message}}", "batchDeleteFailed": "<PERSON><PERSON><PERSON> hàng loạt thất bại: {{message}}", "deleteTokenFailed": "Xóa token thất bại: {{message}}", "refreshTokenFailed": "<PERSON><PERSON><PERSON> mới token thất bại: {{message}}", "enableTokenFailed": "<PERSON><PERSON><PERSON> ho<PERSON> token thất bại: {{message}}", "disableTokenFailed": "<PERSON><PERSON> hi<PERSON>u hóa token thất bại: {{message}}", "fetchDataFailed": "<PERSON><PERSON><PERSON> dữ liệu thất bại: {{message}}"}, "success": {"batchDelete": "<PERSON><PERSON><PERSON> thành công {{count}} token", "shareTextCopied": "<PERSON><PERSON><PERSON> bản chia sẻ đã được sao chép vào clipboard", "tokenCopied": "Token đã đư<PERSON><PERSON> sao chép vào clipboard", "deleteToken": "Xóa token thành công", "refreshToken": "<PERSON><PERSON><PERSON> mới token thành công", "enableToken": "<PERSON><PERSON><PERSON> ho<PERSON> token thành công", "disableToken": "<PERSON><PERSON> hiệu hóa token thành công", "export": "Xuất token trang hiện tại thành công"}, "warning": {"copyFailed": "<PERSON><PERSON> chép thất b<PERSON>i, vui lòng sao chép thủ công", "invalidServerAddress": "<PERSON><PERSON> lòng nhập địa chỉ máy chủ đúng"}, "info": {"openingBridgePage": "<PERSON><PERSON> mở trang kết n<PERSON>, đã sao chép token cho bạn"}, "export": {"name": "<PERSON><PERSON><PERSON>", "key": "Khóa", "billingType": "<PERSON><PERSON><PERSON><PERSON> thức t<PERSON>h phí", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "models": "<PERSON><PERSON> hình có sẵn", "usedQuota": "<PERSON><PERSON><PERSON> mức đã sử dụng", "remainQuota": "<PERSON><PERSON><PERSON> mức còn lại", "createdTime": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "expiredTime": "<PERSON><PERSON><PERSON><PERSON> gian hế<PERSON> hạn", "unlimited": "<PERSON><PERSON><PERSON>ng gi<PERSON>i hạn", "neverExpire": "<PERSON><PERSON><PERSON><PERSON> bao gi<PERSON> hết hạn"}, "billingType": {"1": "<PERSON><PERSON><PERSON> phí theo l<PERSON>", "2": "<PERSON><PERSON><PERSON> ph<PERSON> theo l<PERSON>n", "3": "T<PERSON>h phí hỗn hợp", "4": "Ưu tiên theo l<PERSON>", "5": "Ưu tiên theo lần"}, "bridge": {"quickIntegration": " <PERSON><PERSON><PERSON> n<PERSON>i một cú nhấp"}}, "editTokenModal": {"editTitle": "Chỉnh sửa token", "createTitle": "Tạo token", "defaultTokenName": "Token của {{username}} {{date}}", "tokenName": "Tên token", "unlimitedQuota": "<PERSON><PERSON><PERSON> mức không gi<PERSON>i hạn", "remainingQuota": "<PERSON><PERSON><PERSON> mức còn lại", "authorizedQuota": "<PERSON><PERSON><PERSON> mức <PERSON> quyền", "quotaLimitNote": "<PERSON><PERSON><PERSON> mức tối đa có thể sử dụng của token bị giới hạn bởi số dư tài kho<PERSON>n", "quickOptions": "<PERSON><PERSON><PERSON>h", "neverExpire": "<PERSON><PERSON><PERSON><PERSON> bao gi<PERSON> hết hạn", "expiryTime": "<PERSON><PERSON><PERSON><PERSON> gian hế<PERSON> hạn", "billingMode": "<PERSON><PERSON> độ tính phí", "selectGroup": "<PERSON><PERSON><PERSON>", "switchGroup": "<PERSON><PERSON><PERSON>", "switchGroupTooltip": "<PERSON>ọn nhóm mà token thuộc về, các nhóm khác nhau có giá cả và quyền chức năng khác nhau. <PERSON><PERSON><PERSON> không chọn, sẽ mặc định sử dụng nhóm hiện tại của người dùng", "switchGroupHint": "Vi<PERSON><PERSON> chọn nhóm sẽ ảnh hưởng đến tỷ lệ tính phí và mô hình có sẵn của token, vui lòng chọn theo nhu cầu thực tế", "importantFeature": "<PERSON><PERSON> tr<PERSON>", "tokenRemark": "<PERSON><PERSON> ch<PERSON>", "discordProxy": "Proxy Discord", "enableAdvancedOptions": "<PERSON><PERSON><PERSON> t<PERSON>y chọn nâng cao", "generationAmount": "Số lượng tạo", "availableModels": "<PERSON><PERSON> hình có sẵn", "selectModels": "<PERSON><PERSON><PERSON>/tìm kiếm/thêm mô hình có sẵn, để trống có nghĩa là không giới hạn", "activateOnFirstUse": "<PERSON><PERSON><PERSON> ho<PERSON>t khi sử dụng lần đầu", "activateOnFirstUseTooltip": "<PERSON><PERSON><PERSON> hoạt thời hạn hiệu lực sau lần sử dụng đầu tiên, nếu bật tùy chọn này và kích hoạt thông qua lần sử dụng đầu tiên, sẽ ghi đè thời hạn hiệu lực token được cấu hình ở trên", "activationValidPeriod": "<PERSON><PERSON><PERSON><PERSON> hạn hi<PERSON>u l<PERSON> k<PERSON>ch ho<PERSON>t", "activationValidPeriodTooltip": "<PERSON>h<PERSON><PERSON> hạn hiệu lực token sau lần sử dụng đầu tiên (đơn vị: ngày)", "ipWhitelist": "<PERSON><PERSON> s<PERSON> trắng", "ipWhitelistPlaceholder": "Địa chỉ IP (đoạn), hỗ trợ IPV4 và IPV6, nhiều địa chỉ cách nhau bằng dấu phẩy", "rateLimiter": "Bộ giới hạn tốc độ", "rateLimitPeriod": "<PERSON> kỳ giới hạn tốc độ", "rateLimitPeriodTooltip": "<PERSON> kỳ giới hạn tốc độ (đơn vị: giây)", "rateLimitCount": "Số lần gi<PERSON>i hạn tốc độ", "rateLimitCountTooltip": "Số lần có thể sử dụng trong chu kỳ giới hạn tốc độ", "promptMessage": "<PERSON><PERSON><PERSON><PERSON> báo nh<PERSON>c nhở", "promptMessageTooltip": "<PERSON>h<PERSON><PERSON> bá<PERSON> nhắc nhở khi vượt quá giới hạn tốc độ", "promotionPosition": "<PERSON><PERSON> trí quảng cáo", "promotionPositionStart": "<PERSON><PERSON><PERSON>", "promotionPositionEnd": "<PERSON><PERSON><PERSON><PERSON>", "promotionPositionRandom": "Ngẫu nhiên", "promotionContent": "<PERSON><PERSON>i dung quảng cáo", "currentGroup": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON>n tại", "searchGroupPlaceholder": "<PERSON><PERSON><PERSON> kiếm tên <PERSON>, mô tả hoặc tỷ lệ...", "mjTranslateConfig": "<PERSON><PERSON><PERSON> h<PERSON>nh d<PERSON> M<PERSON>", "mjTranslateConfigTip": "<PERSON><PERSON><PERSON> hình dịch chỉ có hiệu lực với prompt Midjourney", "mjTranslateBaseUrlPlaceholder": "<PERSON><PERSON> lò<PERSON> nhập URL cơ sở của dịch vụ dịch", "mjTranslateApiKeyPlaceholder": "<PERSON><PERSON> lòng nhập khóa API của dịch vụ dịch", "mjTranslateModelPlaceholder": "<PERSON><PERSON> lòng nhập tên mô hình được sử dụng bởi dịch vụ dịch", "mjTranslateBaseUrlRequired": "<PERSON><PERSON><PERSON> cung cấp URL cơ sở khi bật dịch", "mjTranslateApiKeyRequired": "<PERSON>ả<PERSON> cung cấp kh<PERSON>a API khi bật dịch", "mjTranslateModelRequired": "<PERSON><PERSON><PERSON> cung cấp tên mô hình khi bật dịch"}, "addTokenQuotaModal": {"title": "<PERSON><PERSON><PERSON><PERSON> lý số dư token {{username}}", "defaultReason": "<PERSON><PERSON> tác quản trị viên", "enterRechargeAmount": "<PERSON><PERSON> lòng nhập số tiền nạp", "enterRemark": "<PERSON><PERSON> lòng nhập tin nhắn ghi chú", "confirmOperation": "<PERSON><PERSON><PERSON> nh<PERSON>n thao tác", "confirmContent": "<PERSON><PERSON><PERSON> n<PERSON>n {{action}} {{amount}} USD cho {{username}}{{updateExpiry}}?", "recharge": "<PERSON><PERSON><PERSON> ti<PERSON>n", "deduct": "kh<PERSON>u trừ", "andUpdateExpiry": ", và cập nhật thời hạn hiệu lực số dư thành {{days}} ngày", "alertMessage": "<PERSON><PERSON><PERSON><PERSON> số âm có thể khấu trừ số dư người dùng", "rechargeAmount": "<PERSON><PERSON> tiền n<PERSON>p", "operationReason": "Lý do thao tác", "finalBalance": "Số dư cuối cùng"}, "billingType": {"1": "<PERSON><PERSON><PERSON> phí theo l<PERSON>", "2": "<PERSON><PERSON><PERSON> ph<PERSON> theo l<PERSON>n", "3": "T<PERSON>h phí hỗn hợp", "4": "Ưu tiên theo l<PERSON>", "5": "Ưu tiên theo lần", "payAsYouGo": "<PERSON><PERSON><PERSON> phí theo l<PERSON>", "payPerRequest": "<PERSON><PERSON><PERSON> ph<PERSON> theo l<PERSON>n", "hybrid": "T<PERSON>h phí hỗn hợp", "payAsYouGoPriority": "Ưu tiên theo l<PERSON>", "payPerRequestPriority": "Ưu tiên theo lần", "unknown": "<PERSON><PERSON><PERSON><PERSON> thức không xác đ<PERSON>nh"}, "packagePlanAdmin": {"title": "<PERSON><PERSON><PERSON> v<PERSON>", "table": {"title": "<PERSON><PERSON><PERSON><PERSON> lý gói d<PERSON>ch vụ", "toolBar": {"add": "Tạo gói dịch vụ mới", "delete": "Xóa gói d<PERSON> vụ"}, "action": {"edit": "Chỉnh sửa", "delete": "Xóa", "detail": "<PERSON> ti<PERSON>", "recovery": "<PERSON><PERSON><PERSON>", "offline": "Xu<PERSON><PERSON> k<PERSON>"}}, "modal": {"title": {"add": "Tạo gói dịch vụ mới", "edit": "Chỉnh sửa gói d<PERSON>ch vụ"}, "field": {"name": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> d<PERSON> v<PERSON>", "type": {"default": "Loại gói d<PERSON>ch vụ", "type1": "<PERSON><PERSON><PERSON> h<PERSON>n m<PERSON>c", "type2": "<PERSON><PERSON><PERSON>", "type3": "<PERSON><PERSON><PERSON> thời gian"}, "group": "Nhóm gói d<PERSON> vụ", "description": "<PERSON><PERSON> tả gói d<PERSON>ch vụ", "price": "<PERSON><PERSON>á g<PERSON>i d<PERSON>ch vụ", "valid_period": "<PERSON><PERSON><PERSON><PERSON> hạn hi<PERSON> l<PERSON>c", "first_buy_discount": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> lần đầu", "rate_limit_num": "Số lần gi<PERSON>i hạn", "rate_limit_duration": "<PERSON> kỳ giới hạn", "inventory": "<PERSON><PERSON>n kho gói d<PERSON>ch vụ", "available_models": "<PERSON><PERSON> hình có sẵn", "quota": "<PERSON><PERSON><PERSON> m<PERSON>c g<PERSON>i d<PERSON>ch vụ", "times": "Số lần g<PERSON>i d<PERSON>ch vụ"}, "footer": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>"}}}, "login": {"title": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON> dùng", "password": "<PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON>", "otherLoginMethods": "<PERSON><PERSON><PERSON><PERSON> thức đ<PERSON>ng nh<PERSON>p kh<PERSON>c", "register": "<PERSON><PERSON><PERSON> ký tài k<PERSON>n", "accountLogin": "<PERSON><PERSON><PERSON> nhập bằng tài k<PERSON>n", "phoneLogin": "<PERSON><PERSON><PERSON> nhập bằng số điện thoại", "usernamePlaceholder": "<PERSON><PERSON><PERSON> dùng", "usernameRequired": "<PERSON>ui lòng nhập tên người dùng!", "passwordPlaceholder": "<PERSON><PERSON><PERSON>", "passwordRequired": "<PERSON>ui lòng nhập mật khẩu!", "passwordMaxLength": "<PERSON><PERSON> dài mật khẩu không đ<PERSON><PERSON><PERSON> vư<PERSON><PERSON> quá 20 ký tự!", "phonePlaceholder": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "phoneRequired": "<PERSON><PERSON> lòng nhập số điện thoại!", "phoneFormatError": "<PERSON><PERSON><PERSON> dạng số điện thoại không đúng!", "smsCodePlaceholder": "<PERSON><PERSON> x<PERSON>c thực <PERSON>", "smsCodeCountdown": "<PERSON><PERSON><PERSON> lại sau {{count}} gi<PERSON>y", "getSmsCode": "<PERSON><PERSON><PERSON> mã x<PERSON>c thực", "agreementText": "<PERSON><PERSON><PERSON> đồng <PERSON>", "privacyPolicy": "《<PERSON><PERSON><PERSON> s<PERSON>ch b<PERSON>o mật》", "and": "và", "serviceAgreement": "《Thỏa thuận dịch vụ》", "alreadyLoggedIn": "Bạn đã đăng nhập", "weakPasswordWarning": "<PERSON><PERSON><PERSON> kh<PERSON>u của bạn quá đơn giản, vui lòng thay đổi kịp thời!", "welcomeMessage": "<PERSON><PERSON>o mừng sử dụng", "captchaError": "<PERSON><PERSON> xác thực không đúng", "credentialsError": "<PERSON>ên người dùng hoặc mật khẩu không đúng", "resetPassword": "Đặt lại mật khẩu", "captchaExpired": "<PERSON>ã xác thực không tồn tại hoặc đã hết hạn", "loginFailed": "<PERSON><PERSON><PERSON> nhập thất bại: {{message}}", "captchaRequired": "Vui lòng nhập mã xác thực!", "captchaPlaceholder": "<PERSON><PERSON> xác thực", "smsSent": "<PERSON><PERSON><PERSON> mã x<PERSON>c thực SMS thành công", "smsSendFailed": "<PERSON><PERSON><PERSON> mã x<PERSON>c thực SMS thất bại", "agreementWarning": "Vui lòng đồng ý với 《<PERSON>ính sách bảo mật》 và 《Thỏa thuận dịch vụ》 trước", "turnstileWarning": "<PERSON><PERSON> lò<PERSON> thử lại sau, <PERSON><PERSON><PERSON> đang kiểm tra môi trường người dùng!", "loginSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>p thành công"}, "register": {"title": "<PERSON><PERSON><PERSON> ký", "usernameRequired": "<PERSON>ui lòng nhập tên người dùng!", "usernameNoAt": "Tên người dùng không đ<PERSON><PERSON><PERSON> chứa ký hiệu @", "usernameNoChinese": "Tên người dùng không đư<PERSON><PERSON> chứa ký tự tiếng Trung", "usernameLength": "<PERSON><PERSON> dài tên người dùng phải từ 4-12 ký tự", "usernamePlaceholder": "<PERSON><PERSON><PERSON> dùng", "passwordRequired": "<PERSON>ui lòng nhập mật khẩu!", "passwordLength": "<PERSON><PERSON> dài mật khẩu phải từ 8-20 ký tự", "passwordPlaceholder": "<PERSON><PERSON><PERSON>", "confirmPasswordRequired": "<PERSON>ui lòng xác nhận mật khẩu!", "passwordMismatch": "<PERSON> lần nhập mật khẩu không khớp!", "confirmPasswordPlaceholder": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "emailInvalid": "<PERSON><PERSON> lòng nhập địa chỉ email hợp lệ!", "emailRequired": "Vui lòng nhập email!", "emailPlaceholder": "Đ<PERSON>a chỉ email", "emailCodeRequired": "Vui lòng nhập mã xác thực email!", "emailCodePlaceholder": "Mã xác thực email", "enterCaptcha": "<PERSON><PERSON> lòng nhập mã xác thực", "resendEmailCode": "G<PERSON>i lại sau {{seconds}} gi<PERSON>y", "getEmailCode": "<PERSON><PERSON><PERSON> mã x<PERSON>c thực", "phoneRequired": "<PERSON><PERSON> lòng nhập số điện thoại!", "phoneInvalid": "<PERSON><PERSON><PERSON> dạng số điện thoại không đúng!", "phonePlaceholder": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "smsCodeRequired": "Vui lòng nhập mã xác thực SMS!", "smsCodePlaceholder": "<PERSON><PERSON> x<PERSON>c thực <PERSON>", "resendSmsCode": "G<PERSON>i lại sau {{seconds}} gi<PERSON>y", "getSmsCode": "<PERSON><PERSON><PERSON> mã x<PERSON>c thực", "captchaRequired": "Vui lòng nhập mã xác thực!", "captchaPlaceholder": "<PERSON><PERSON> xác thực", "inviteCodePlaceholder": "<PERSON><PERSON> m<PERSON> (t<PERSON><PERSON>)", "submit": "<PERSON><PERSON><PERSON> ký", "successMessage": "<PERSON><PERSON><PERSON> ký thành công", "failMessage": "<PERSON><PERSON><PERSON> ký thất bại", "emailCodeSent": "<PERSON><PERSON> xác thực email đã đ<PERSON><PERSON><PERSON> g<PERSON>i", "smsCodeSent": "<PERSON><PERSON> x<PERSON>c thực SMS đã đ<PERSON><PERSON><PERSON> g<PERSON>i", "confirm": "<PERSON><PERSON><PERSON>", "emailVerifyTitle": "<PERSON><PERSON><PERSON> thực email", "smsVerifyTitle": "<PERSON><PERSON><PERSON> th<PERSON>c <PERSON>", "registerVerifyTitle": "<PERSON><PERSON><PERSON> thực đ<PERSON>ng ký"}, "profile": {"timezone": "<PERSON><PERSON><PERSON> giờ", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "emailAddress": "Đ<PERSON>a chỉ email", "wechatAccount": "<PERSON><PERSON><PERSON>hat", "telegramAccount": "Tài khoản Telegram", "bindTelegram": "<PERSON><PERSON>n kết Telegram", "balanceValidPeriod": "<PERSON><PERSON><PERSON><PERSON> hạn hi<PERSON>u lực số dư", "lastLoginIP": "IP đăng nhập lần cu<PERSON>i", "lastLoginTime": "<PERSON>h<PERSON><PERSON> gian đ<PERSON>ng nhập lần cuối", "inviteCode": "Mã mời", "inviteLink": "<PERSON><PERSON><PERSON> kết mời", "generate": "Tạo", "pendingEarnings": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> chờ sử dụng", "transfer": "<PERSON><PERSON><PERSON><PERSON>", "totalEarnings": "<PERSON><PERSON><PERSON> thu nhập", "accountBalance": "Số dư tài <PERSON>", "totalConsumption": "<PERSON><PERSON><PERSON> tiêu thụ", "callCount": "Số lần g<PERSON>i", "invitedUsers": "<PERSON>ư<PERSON><PERSON> dùng đ<PERSON><PERSON><PERSON> mời", "promotionInfo": "Th<PERSON>ng tin khu<PERSON>ến mãi", "inviteDescription": "<PERSON><PERSON><PERSON> l<PERSON><PERSON> mời, hoa hồng tr<PERSON><PERSON> đờ<PERSON>, mờ<PERSON> càng nhi<PERSON>, hoa hồng càng nhiều", "userInfo": "Thông tin người dùng", "availableModels": "<PERSON><PERSON> hình có sẵn", "modelNameCopied": "Đã sao chép tên mô hình", "noAvailableModels": "<PERSON><PERSON><PERSON> có mô hình khả dụng", "accountOptions": "<PERSON><PERSON><PERSON> ch<PERSON>n tài <PERSON>n", "changePassword": "<PERSON>hay đ<PERSON>i mật kh<PERSON>u", "systemToken": "<PERSON><PERSON> hệ thống", "accessTokens": "Token truy cập", "unsubscribe": "<PERSON><PERSON><PERSON> đ<PERSON>ng ký", "educationCertification": "<PERSON><PERSON><PERSON> nhận gi<PERSON><PERSON>", "timezoneUpdateSuccess": "<PERSON><PERSON><PERSON> nhật múi giờ thành công", "inviteLinkCopied": "Đã sao chép liên kết mời", "inviteLinkCopyFailed": "<PERSON><PERSON> ch<PERSON><PERSON> liên kết mời thất bại", "inviteLinkGenerationFailed": "<PERSON><PERSON><PERSON> liên kết mời thất bại", "allModelsCopied": "Tất cả mô hình đã đư<PERSON><PERSON> sao chép vào clipboard", "copyAllModels": "<PERSON><PERSON> ch<PERSON>p tất cả mô hình", "totalModels": "S<PERSON> lượng mô hình có sẵn", "expired": "<PERSON><PERSON> hết hạn", "validPeriod": "<PERSON><PERSON><PERSON><PERSON> hạn hi<PERSON> l<PERSON>c", "longTermValid": "<PERSON><PERSON><PERSON> l<PERSON> dài hạn", "failedToLoadModels": "<PERSON><PERSON><PERSON> danh sách mô hình thất bại", "accessTokensManagement": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> truy cập", "accessTokenDescription": "Token truy cập được sử dụng để xác thực truy cập <PERSON>, có quyền cụ thể liên quan đến tài khoản của bạn", "tokenNameLabel": "Tên token", "tokenNamePlaceholder": "Đặt tên cho token, ví dụ: token chỉ đọc, token công cụ kiểm tra, v.v.", "presetPermissions": "Quyền đặt trước", "detailPermissions": "<PERSON><PERSON><PERSON><PERSON> chi tiết", "validityPeriod": "<PERSON><PERSON><PERSON><PERSON> hạn hi<PERSON> (ngày)", "validityPeriodExtra": "0 có ngh<PERSON>a là không bao giờ hết hạn", "remarkLabel": "<PERSON><PERSON><PERSON>", "remarkPlaceholder": "<PERSON><PERSON><PERSON>, mô tả mục đích sử dụng token, v.v.", "createNewToken": "Tạo token mới", "tokenCreatedSuccess": "Tạo token truy cập thành công", "tokenSavePrompt": "<PERSON><PERSON> lòng lưu token này cẩn thận, nó chỉ hiển thị một lần!", "copyToken": "Sao chép token", "readPermission": "<PERSON><PERSON><PERSON><PERSON>", "writePermission": "<PERSON><PERSON><PERSON><PERSON> ghi", "deletePermission": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "tokenManagement": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "channelManagement": "<PERSON><PERSON><PERSON><PERSON> lý k<PERSON>nh", "logView": "<PERSON><PERSON> ký", "statisticsView": "<PERSON><PERSON> th<PERSON> kê", "userManagement": "<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON><PERSON> dùng", "quotaManagement": "<PERSON><PERSON><PERSON><PERSON> lý hạn mức", "readOnlyPermission": "Quyền chỉ đọc", "writeOnlyPermission": "Quyền chỉ ghi", "readWritePermission": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>hi", "standardPermission": "<PERSON><PERSON><PERSON><PERSON> tiêu chuẩn", "fullPermission": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>y đủ", "selectPermission": "<PERSON><PERSON> lòng chọn ít nhất một quyền", "tokenStatus": "<PERSON><PERSON><PERSON><PERSON> thái", "tokenEnabled": "<PERSON><PERSON><PERSON>", "tokenDisabled": "Tắt", "enableToken": "<PERSON><PERSON><PERSON>", "disableToken": "Tắt", "deleteToken": "Xóa", "deleteTokenConfirm": "Bạn có chắc chắn muốn xóa token truy cập \"{{name}}\" không?", "disableTokenConfirm": "Bạn có chắc chắn muốn tắt token truy cập \"{{name}}\" không?", "enableTokenConfirm": "Bạn có chắc chắn muốn bật token truy cập \"{{name}}\" không?", "tokenExpiryNever": "<PERSON><PERSON><PERSON><PERSON> bao gi<PERSON> hết hạn", "accessTokensInfo": "Thông tin token truy cập", "accessTokensInfoDetail1": "Token truy cập được sử dụng để xác thực truy cập <PERSON>, có quyền cụ thể liên quan đến tài khoản của bạn", "accessTokensInfoDetail2": "Mỗi token có thể được cấu hình với các quyền khác nhau để đáp ứng các tình huống sử dụng khác nhau", "accessTokensInfoDetail3": "<PERSON><PERSON> lý do bảo mật, token chỉ hiển thị một lần khi tạo, vui lòng lưu cẩn thận", "accessTokensInfoDetail4": "<PERSON><PERSON> lòng tắt hoặc xóa kịp thời các token không còn sử dụng", "accessTokensInfoDetail5": "<PERSON><PERSON><PERSON> tư cách là quản trị viên cấp cao, bạn có thể cấu hình tất cả các quyền nâng cao", "noPermission": "<PERSON><PERSON><PERSON><PERSON> có quyền thực hiện thao tác này"}, "topup": {"onlineRecharge": "<PERSON><PERSON><PERSON> tiền trự<PERSON> tuyến", "cardRedemption": "Đổi mã thẻ", "accountBalance": "Số dư tài <PERSON>", "rechargeReminder": "Nhắc nhở nạp tiền", "reminder1": "1. <PERSON><PERSON> dư có thể dùng để gọi mô hình, mua gói dịch vụ, v.v.", "reminder2": "2. <PERSON><PERSON><PERSON> sau khi thanh toán mà số dư chưa đ<PERSON><PERSON><PERSON> c<PERSON>, vui lòng liên hệ dịch vụ khách hàng", "reminder3": "3. <PERSON><PERSON> dư không hỗ trợ rút tiền, nh<PERSON><PERSON> có thể chuyển khoản trong cùng nhóm người dùng", "reminder4WithTransfer": "4. <PERSON><PERSON> <PERSON><PERSON> nạ<PERSON> tiền thành công, thời hạn hiệu lực số dư tài khoản sẽ được đặt lại thành", "reminder4WithoutTransfer": "3. <PERSON><PERSON> <PERSON><PERSON> nạ<PERSON> tiền thành công, thời hạn hiệu lực số dư tài khoản sẽ được đặt lại thành", "days": "ng<PERSON>y", "paymentSuccess": "<PERSON><PERSON> to<PERSON> thành công", "paymentError": "Lỗi thanh toán", "paymentAmount": "Số tiền thanh toán:", "purchaseAmount": "Số tiền mua: $ ", "yuan": "nhân dân tệ", "or": "hoặc", "usd": "USD", "cny": "nhân dân tệ", "enterAmount": "<PERSON><PERSON> lòng nhập số tiền nạp!", "amountPlaceholder": "<PERSON><PERSON> lòng nhập số tiền nạp, tối thiểu {{min}} USD", "amountUpdateError": "Lỗi khi cập nhật số tiền", "alipay": "Alipay", "wechat": "WeChat", "visaMastercard": "Visa / Mastercard", "cardFormatError": "Định dạng mã đổi thưởng không đúng", "redeemSuccess": "Đổi {{amount}} thành công!", "redeemError": "Lỗi đổi thưởng, vui lòng thử lại sau", "enterCardKey": "<PERSON><PERSON> lòng nhập mã thẻ đổi thưởng", "cardKeyPlaceholder": "<PERSON><PERSON> lòng nhập mã thẻ đổi thưởng", "buyCardKey": "<PERSON>a mã thẻ đổi thưởng", "redeem": "<PERSON><PERSON><PERSON> ngay", "record": {"title": "<PERSON><PERSON><PERSON> sử nạp tiền", "amount": "<PERSON><PERSON> tiền n<PERSON>p", "payment": "<PERSON><PERSON> tiền thanh toán", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "orderNo": "<PERSON><PERSON> đơn hàng", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createTime": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "statusSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "statusPending": "<PERSON><PERSON> lý", "statusFailed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "paymentMethodAlipay": "Alipay", "paymentMethodWxpay": "WeChat", "paymentMethodUSDT_TRC20": "USDT", "paymentMethodEVM_ETH_ETH": "ETH", "paymentMethodPaypal": "PayPal", "paymentMethodAdmin": "<PERSON><PERSON><PERSON><PERSON> trị viên", "paymentMethodRedeem": "<PERSON>ã đổi thưởng", "alipayF2F": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON> ti<PERSON>p"}, "pricing": {"fetchErrorMessage": "<PERSON><PERSON><PERSON> thông tin giá cả có lỗi, vui lòng liên hệ quản trị viên", "availableModelErrorMessage": "<PERSON><PERSON><PERSON> mô hình có sẵn có lỗi, vui lòng liên hệ quản trị viên", "modelName": "<PERSON><PERSON><PERSON> mô hình", "billingType": "Loại t<PERSON>h phí", "price": "Giá", "ratio": "Tỷ lệ", "promptPriceSame": "Giá prompt: gi<PERSON><PERSON> với tỷ lệ gốc", "completionPriceSame": "Giá completion: gi<PERSON><PERSON> với tỷ lệ gốc", "promptPrice": "Giá prompt: $ {{price}} / 1M tokens", "completionPrice": "Giá completion: $ {{price}} / 1M tokens", "promptRatioSame": "Tỷ lệ prompt: g<PERSON><PERSON><PERSON> với tỷ lệ gốc", "completionRatioSame": "Tỷ lệ completion: g<PERSON><PERSON><PERSON> với tỷ lệ gốc", "promptRatio": "Tỷ lệ prompt: {{ratio}}", "completionRatio": "Tỷ lệ completion: {{ratio}}", "payAsYouGo": "<PERSON><PERSON><PERSON> theo l<PERSON> sử dụng - <PERSON>", "fixedPrice": "$ {{price}} / lần", "payPerRequest": "<PERSON><PERSON><PERSON> theo y<PERSON> c<PERSON>", "dynamicPrice": "$ {{price}} / lần", "payPerRequestAPI": "<PERSON><PERSON><PERSON> theo yêu cầu - API", "loadingTip": "<PERSON><PERSON> lấy thông tin giá cả...", "userGroupRatio": "Tỷ lệ nhóm người dùng của bạn là: {{ratio}}", "readFailed": "<PERSON><PERSON><PERSON> thất b<PERSON>i", "billingFormula": "<PERSON><PERSON> tính theo l<PERSON> = Tỷ lệ chuyển đổi × Tỷ lệ nhóm × Tỷ lệ mô hình × (Số token prompt + <PERSON><PERSON> token completion × Tỷ lệ completion) / 500000 (đơn vị: USD)", "billingFormula1": "Tỷ lệ chuyển đổi = (Tỷ lệ nạp mới/Tỷ lệ nạp cũ) × (Tỷ lệ nhóm mới/Tỷ lệ nhóm cũ)", "generatedBy": "<PERSON>rang này đư<PERSON><PERSON> tạo tự động bởi {{systemName}}", "modalTitle": "<PERSON> tiết giá cả", "perMillionTokens": "/1M tokens", "close": "Đ<PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm tên mô hình", "viewGroups": "<PERSON><PERSON>", "copiedToClipboard": "Đã sao chép vào clipboard", "copyFailed": "<PERSON><PERSON> ch<PERSON>p thất b<PERSON>i", "groupName": "<PERSON><PERSON><PERSON>", "availableGroups": "<PERSON><PERSON><PERSON> nhóm có sẵn cho mô hình {{model}}", "noGroupsAvailable": "<PERSON>hông có nhóm nào có sẵn", "modelGroupsErrorMessage": "<PERSON><PERSON><PERSON> dữ liệu nhóm mô hình thất bại", "currentGroup": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON>n tại", "copyModelName": "<PERSON><PERSON> chép tên mô hình", "groupRatio": "Tỷ lệ nhóm", "closeModal": "Đ<PERSON><PERSON>", "groupsForModel": "Nhóm có sẵn cho mô hình", "actions": "<PERSON><PERSON>", "filterByGroup": "<PERSON><PERSON><PERSON> the<PERSON>", "groupSwitched": "<PERSON><PERSON> ch<PERSON> sang nhóm: {{group}}", "showAdjustedPrice": "Hiển thị giá đã điều chỉnh theo nhóm (tỷ lệ hiện tại: {{ratio}})"}, "guestQuery": {"usageTime": "Thời gian sử dụng", "modelName": "<PERSON><PERSON><PERSON> mô hình", "promptTooltip": "Token tiêu thụ đầu vào", "completionTooltip": "Token tiêu thụ đầu ra", "quotaConsumed": "<PERSON><PERSON><PERSON> mức tiêu thụ", "pasteConfirm": "<PERSON><PERSON><PERSON> hiện token hợp lệ trong clipboard, bạn có muốn dán không?", "queryFailed": "<PERSON><PERSON><PERSON> vấn thất bại", "tokenExpired": "Token này đã hết hạn", "tokenExhausted": "<PERSON><PERSON><PERSON> mức token này đã cạn kiệt", "invalidToken": "<PERSON><PERSON> lòng nhập token đúng", "focusRequired": "<PERSON><PERSON> lòng đảm bảo trang đang đư<PERSON> focus", "queryFirst": "<PERSON><PERSON> lòng truy vấn trước", "tokenInfoText": "Tổng token: {{totalQuota}}\nToken đã dùng: {{usedQuota}}\nToken còn lại: {{remainQuota}}\n<PERSON><PERSON> lần gọi: {{callCount}}\nHi<PERSON><PERSON> lực đến: {{validUntil}}", "unlimited": "<PERSON><PERSON><PERSON>ng gi<PERSON>i hạn", "neverExpire": "<PERSON><PERSON><PERSON><PERSON> bao gi<PERSON> hết hạn", "infoCopied": "Thông tin token đã đư<PERSON>c sao chép vào clipboard", "copyFailed": "<PERSON><PERSON> ch<PERSON>p thất b<PERSON>i", "noDataToExport": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu để xuất", "prompt": "Prompt", "completion": "Completion", "disabled": "<PERSON><PERSON><PERSON> v<PERSON>n kh<PERSON>ch ch<PERSON>a đ<PERSON><PERSON><PERSON> bật", "tokenQuery": "<PERSON><PERSON><PERSON> vấn token", "tokenPlaceholder": "<PERSON><PERSON> lòng nhập token cần truy vấn (sk-xxx)", "tokenInfo": "Thông tin token", "copyInfo": "<PERSON>o chép thông tin", "totalQuota": "Tổng token", "usedQuota": "Token đã dùng", "remainQuota": "Token còn lại", "callCount": "Số lần g<PERSON>i", "validUntil": "<PERSON><PERSON><PERSON> l<PERSON>", "currentRPM": "RPM hiện tại", "currentTPM": "TPM hiện tại", "callLogs": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "exportLogs": "<PERSON><PERSON><PERSON> n<PERSON>t ký"}, "agencyProfile": {"fetchError": "<PERSON><PERSON><PERSON> thông tin đại lý thất bại", "fetchCommissionError": "<PERSON><PERSON><PERSON> danh sách hoa hồng thất bại", "systemPreset": "<PERSON><PERSON><PERSON> đặt hệ thống", "lowerRatioWarning": "Tỷ lệ thấp hơn cài đặt hệ thống", "lowerRatioMessage": "<PERSON><PERSON><PERSON> tỷ lệ sau thấp hơn giá trị cài đặt hệ thống, vui lòng sửa đổi kịp thời:", "cancelRatioEdit": "<PERSON><PERSON><PERSON> sửa đổi tỷ lệ", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công", "updateError": "<PERSON><PERSON><PERSON> nhật thông tin đại lý thất bại:", "updateFailed": "<PERSON><PERSON><PERSON> nhật thất bại: ", "customPriceUpdateSuccess": "<PERSON><PERSON><PERSON> nhật giá tùy chỉnh thành công", "customPriceUpdateError": "<PERSON><PERSON><PERSON> nhật giá tùy chỉnh thất bại:", "time": "<PERSON><PERSON><PERSON><PERSON> gian", "type": "<PERSON><PERSON><PERSON>", "agencyCommission": "<PERSON><PERSON> hồng đại lý", "unknownType": "<PERSON><PERSON><PERSON> không x<PERSON>c đ<PERSON>", "amount": "<PERSON><PERSON> tiền", "balance": "Số dư", "description": "<PERSON><PERSON>", "group": "Nhóm", "customRate": "Tỷ lệ tùy chỉnh", "systemDefaultRate": "Tỷ lệ mặc định hệ thống", "action": "<PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "agencyConsole": "<PERSON><PERSON><PERSON> điều khiển đại lý", "agencyInfo": "<PERSON>h<PERSON><PERSON> tin đại lý", "editInfo": "Chỉnh sửa thông tin", "agencyName": "<PERSON><PERSON><PERSON> đ<PERSON> lý", "agencyLevel": "<PERSON><PERSON><PERSON> độ đại lý", "level1": "Cấp 1", "subordinateUsers": "<PERSON><PERSON><PERSON><PERSON> dùng cấp dư<PERSON>", "totalSales": "<PERSON><PERSON><PERSON> do<PERSON>h số", "commissionIncome": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> hoa hồng", "cumulativeEarnings": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> tích l<PERSON>y", "agencyFunctions": "<PERSON><PERSON><PERSON> năng đại lý", "hideSubordinateUsers": "Ẩn người dùng cấp dưới", "viewSubordinateUsers": "<PERSON><PERSON> dùng cấp dư<PERSON>", "hideCommissionDetails": "Ẩn chi tiết hoa hồng", "viewCommissionDetails": "<PERSON>em chi tiết hoa hồng", "hideCustomPrice": "Ẩn giá tùy chỉnh", "setCustomPrice": "<PERSON><PERSON><PERSON><PERSON> lập giá tùy chỉnh", "subordinateUsersList": "<PERSON><PERSON> s<PERSON>ch người dùng cấp dư<PERSON>i", "commissionRecords": "<PERSON><PERSON> sơ hoa hồng", "customPriceSettings": "Cài đặt giá tùy chỉnh", "saveChanges": "<PERSON><PERSON><PERSON> thay đổi", "editAgencyInfo": "Chỉnh sửa thông tin đại lý", "logo": "Logo", "setAgencyLogo": "<PERSON><PERSON><PERSON><PERSON> lập <PERSON> lý", "customHomepage": "Trang chủ tùy chỉnh", "aboutContent": "<PERSON><PERSON><PERSON> dung giới thiệu", "newHomepageConfig": "<PERSON><PERSON><PERSON> hình trang chủ mới", "customAnnouncement": "<PERSON>h<PERSON>ng báo tùy chỉnh", "customRechargeGroupRateJson": "JSON tỷ lệ nhóm nạp tiền tùy chỉnh", "customRechargeRate": "Tỷ lệ nạp tiền tùy chỉnh", "viewSystemDefaultRate": "<PERSON>em tỷ lệ mặc định hệ thống", "rateComparison": "So sánh tỷ lệ", "comparisonResult": "<PERSON><PERSON><PERSON> quả so s<PERSON>h", "higherThanSystem": "<PERSON> hơn hệ thống", "lowerThanSystem": "<PERSON><PERSON><PERSON><PERSON> hơn hệ thống", "equalToSystem": "Bằng hệ thống", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "notAnAgentYet": "Bạn chưa phải là đại lý", "becomeAnAgent": "Trở thành đại lý", "startYourOnlineBusiness": "🌟 <PERSON><PERSON> dàng khởi nghiệp kinh doanh trực tuyến", "becomeOurAgent": "Trở thành đại lý của chúng tôi, tận hưởng trải nghiệm khởi nghiệp không áp lực:", "noInventory": "💼 Không cần tồn kho, không áp lực vốn lưu động", "instantCommission": "💰 Chia sẻ doanh thu ngay lập tức, nhận lợi nhuận hấp dẫn theo tỷ lệ", "easyManagement": "🖥️ <PERSON>hông cần kỹ thuật xây dựng website, dễ dàng quản lý cửa hàng trự<PERSON> tuyến", "flexibleDomainChoice": "🌐 L<PERSON>a chọn tên miền linh hoạt", "youCan": "<PERSON><PERSON>n có thể:", "useOwnDomain": "🏠 Sử dụng tên miền riêng", "orUseOurSubdomain": "🎁 Hoặc chúng tôi cung cấp tên miền phụ độc quyền cho bạn", "convenientStart": "🔥 Dù bạn có kinh nghiệm hay mới bắt đầu, chúng tôi đều cung cấp cách thức khởi đầu thuận tiện.", "actNow": "🚀 Hành động ngay!", "contactAdmin": "<PERSON>ên hệ quản trị viên website, bắt đầu hành trình đại lý của bạn! 📞", "applyNow": "<PERSON><PERSON><PERSON> ký ngay", "contactCooperation": "<PERSON><PERSON> v<PERSON>n h<PERSON> t<PERSON>c", "understandPolicy": "<PERSON><PERSON><PERSON> hi<PERSON><PERSON> ch<PERSON>h sách đại lý và chi tiết hợp tác", "provideDomain": "<PERSON><PERSON> cấp tên mi<PERSON>n", "configDomain": "<PERSON><PERSON> cấp tên miền của bạn, chúng tôi sẽ gi<PERSON><PERSON> cấu hình", "promoteAndEarn": "<PERSON><PERSON><PERSON><PERSON> bá và kiếm lợi nhu<PERSON>n", "startPromoting": "<PERSON><PERSON><PERSON> đầu quảng bá trang đại lý của bạn, ki<PERSON><PERSON> hoa hồng", "noDeploymentWorries": "<PERSON><PERSON><PERSON><PERSON> cần lo lắng về triển khai dịch vụ đám mây phứ<PERSON> tạp, kê<PERSON>h to<PERSON>, vấn đề tồn kho", "easySetup": "Chỉ cần cung cấp tê<PERSON> mi<PERSON>, là<PERSON> theo hướng dẫn cấu hình, có thể dễ dàng khởi động kinh doanh đại lý API cấp doanh nghiệp", "customizeContent": "Bạn có thể tùy chỉnh gi<PERSON> cả, thông tin trang web, SEO, Logo và các nội dung khác", "commissionBenefits": "<PERSON><PERSON><PERSON> tư cách đại lý, bạn sẽ nhận được chia sẻ từ việc nạp tiền của người dùng, hệ thống tự động khấu trừ chi phí, số tiền còn lại có thể rút bất cứ lúc nào", "joinNowBenefit": "Hãy tham gia với chúng tôi ngay bây giờ, cùng nhau tận hưởng cơ hội của thời đại AI!", "groups": {"student": "<PERSON><PERSON> viên đạ<PERSON> học", "studentDesc": "<PERSON><PERSON> thời gian <PERSON>nh rỗi, muốn dễ dàng tăng thu nhập thông qua hoạt động quảng bá để trang trải một phần chi phí sinh hoạt và giải trí", "partTime": "<PERSON><PERSON><PERSON> thêm hoặc nghề phụ", "partTimeDesc": "<PERSON><PERSON><PERSON>ng cần đầu tư nhiều thời gian, chỉ cần quảng bá đơn giản trong thời gian rảnh rỗi, có thể dễ dàng kiếm thêm thu nhập", "mediaWorker": "<PERSON>ư<PERSON><PERSON> làm t<PERSON>ền thông tự do", "mediaWorkerDesc": "<PERSON><PERSON> một lượng fan nhất định, chỉ cần đính kèm liên kết ở cuối bài viết hoặc bài đăng, có thể dễ dàng tạo ra thu nhập bổ sung", "freelancer": "Freelancer", "freelancerDesc": "<PERSON><PERSON> nhi<PERSON>u thời gian <PERSON>, chỉ cần tham gia các hoạt động bán hàng, có thể dễ dàng tăng thu nhập bổ sung"}, "stories": {"story1": {"name": "<PERSON><PERSON>", "role": "<PERSON><PERSON> viên đạ<PERSON> học"}, "story2": {"name": "<PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON><PERSON> viên trung học"}, "story3": {"name": "<PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại điện tử"}, "story4": {"name": "<PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON><PERSON><PERSON> thông tự do"}, "story5": {"name": "<PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON> viên nghiên cứu"}, "story6": {"name": "Chị <PERSON>", "role": "Blogger <PERSON><PERSON><PERSON>"}, "story7": {"name": "<PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON><PERSON><PERSON> thông tự do"}, "story8": {"name": "<PERSON><PERSON>", "role": "Ngành IT"}}, "earnedAmount": "<PERSON><PERSON> kiếm đ<PERSON> {{amount}}", "applyForAgentNow": "<PERSON><PERSON><PERSON> ký trở thành đại lý ngay", "businessLinesConnected": "Hơn 40 dòng kinh doanh đã đ<PERSON><PERSON><PERSON> kết nối", "agencyJoin": "<PERSON><PERSON> <PERSON>h<PERSON><PERSON> đạ<PERSON> lý", "becomeExclusiveAgent": "Trở thành đại lý độc quyền của chúng tôi", "startBusinessJourney": "<PERSON><PERSON> dàng bắt đầu hành trình kinh doanh của bạn~", "welcomeToAgencyPage": "Chào mừng đến với trang đại lý của chúng tôi!", "earningsTitle": "Hơn 100 người đã kiếm được 3000+ nhân dân tệ", "becomeAgentSteps": "<PERSON><PERSON><PERSON> bước trở thành đại lý", "agencyRules": "<PERSON><PERSON> tắc đại lý", "suitableGroups": "<PERSON>hóm phù hợp", "agencyImages": {"becomeAgent": "Trở thành đại lý", "agencyBusiness": "<PERSON><PERSON> do<PERSON>h đại lý"}, "rules": {"howToEstablishRelation": "<PERSON><PERSON><PERSON>i dùng thiết lập mối quan hệ đại lý với tôi như thế nào", "howToEstablishRelationAnswer": "<PERSON><PERSON>ng ký trên trang đại lý của bạn, tức là trở thành người dùng của bạn", "canSetPrice": "<PERSON><PERSON><PERSON> có thể thiết lập giá bán không", "canSetPriceAnswer": "Có thể! Nhưng giá bán của bạn phải cao hơn giá nhập 10%", "commissionShare": "<PERSON><PERSON><PERSON> có thể nhận đ<PERSON><PERSON><PERSON> bao nhiều phần trăm hoa hồng", "commissionShareAnswer": {"assumption": "Giả sử: g<PERSON><PERSON> nhập của bạn là $1=1 nhân dân tệ, gi<PERSON> bán của bạn là $1=2 nhân dân tệ, tỷ lệ hoa hồng của bạn là 90%", "example": "Ngư<PERSON>i dùng mua $10 trên trang của bạn, ti<PERSON><PERSON> thụ 20 nhân dân tệ", "calculation": "<PERSON><PERSON><PERSON> có thể nhận được: (2-1)*10*0.9 = 9 nhân dân tệ", "explanation": "G<PERSON><PERSON>i thích: (<PERSON><PERSON><PERSON> bán - <PERSON><PERSON><PERSON> nhập) * Kh<PERSON><PERSON> lượng giao dịch * Tỷ lệ hoa hồng"}}}, "error": {"title": "Lỗi", "content": "Đ<PERSON> xảy ra lỗi"}, "loading": {"title": "<PERSON><PERSON> t<PERSON>", "content": "<PERSON><PERSON> tả<PERSON>..."}, "notfound": {"title": "404", "content": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trang"}, "servererror": {"title": "500", "content": "Lỗi máy chủ"}, "unauthorized": {"title": "401", "content": "<PERSON><PERSON><PERSON><PERSON> quyền"}, "forbidden": {"title": "403", "content": "<PERSON><PERSON><PERSON> truy c<PERSON>p"}, "networkerror": {"title": "Lỗi mạng", "content": "Lỗi mạng"}, "timeout": {"title": "<PERSON><PERSON><PERSON> thời gian", "content": "<PERSON><PERSON><PERSON> c<PERSON>u hết thời gian"}, "noresult": {"title": "<PERSON><PERSON><PERSON><PERSON> có kết quả", "content": "<PERSON><PERSON><PERSON><PERSON> có kết quả"}, "nopermission": {"title": "<PERSON><PERSON><PERSON><PERSON> có quyền", "content": "<PERSON><PERSON><PERSON><PERSON> có quyền"}, "channelBridge": {"title": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> n<PERSON>h kênh", "channelPlatform": "<PERSON><PERSON><PERSON> tảng kênh", "billingMethod": "<PERSON><PERSON><PERSON><PERSON> thức t<PERSON>h phí", "channelName": "<PERSON><PERSON><PERSON> k<PERSON>", "remark": "<PERSON><PERSON><PERSON>", "availableGroups": "Nhóm có sẵn", "availableModels": "<PERSON><PERSON> hình có sẵn", "channelKey": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "proxyAddress": "Địa chỉ kết nối", "cancel": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "gpt35Models": "Mô hình GPT-3.5", "gpt4Models": "Mô hình GPT-4", "clear": "Xóa", "customModelName": "Tên mô hình tùy chỉnh", "add": "<PERSON><PERSON><PERSON><PERSON>", "moreConfigReminder": "<PERSON><PERSON> cấu hình thêm, vui lòng lưu kênh trước rồi chỉnh sửa", "quickIntegration": "<PERSON><PERSON><PERSON> n<PERSON>i một cú nhấp", "selectBillingMethod": "<PERSON><PERSON> lòng chọn ph<PERSON><PERSON><PERSON> thức t<PERSON>h phí", "enterChannelName": "<PERSON><PERSON> lòng nhập tên kênh", "enterChannelRemark": "<PERSON><PERSON> lòng nh<PERSON>p ghi chú kênh", "selectAvailableGroups": "<PERSON>ui lòng chọn nhóm có thể sử dụng kênh này", "selectAvailableModels": "Ch<PERSON>n/tìm kiếm mô hình có sẵn cho kênh này", "enterChannelKey": "<PERSON><PERSON> lòng nh<PERSON>p kh<PERSON><PERSON> kênh", "proxyAddressPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>à<PERSON> tù<PERSON>, dùng để thực hiện gọi API thông qua trang proxy, vui lòng nhập địa chỉ trang proxy", "includes16kModels": "<PERSON><PERSON> gồm mô hình 16k", "excludes32kModels": "<PERSON><PERSON><PERSON><PERSON> bao gồm mô hình 32k", "cleared": "Đã xóa", "addCustomModel": "Thêm mô hình tùy chỉnh", "clipboardTokenDetected": "<PERSON><PERSON><PERSON> hiện token hợp lệ trong clipboard, bạn có muốn dán không?", "channelIntegrationSuccess": "<PERSON>ết n<PERSON>i kênh thành công!", "channelIntegrationFailed": "<PERSON><PERSON><PERSON> n<PERSON>i kênh thất bại:"}, "about": {"loading": "<PERSON><PERSON> lấy nội dung mới nhất...", "noContent": "<PERSON><PERSON><PERSON><PERSON> trị viên chưa thiết lập nội dung trang giới thiệu", "loadFailed": "<PERSON><PERSON><PERSON> nội dung giới thiệu thất bại..."}, "onlineTopupRecord": {"title": "<PERSON><PERSON><PERSON> sử nạp tiền", "columns": {"id": "ID", "username": "<PERSON><PERSON><PERSON><PERSON> dùng", "amount": "<PERSON><PERSON> tiền n<PERSON>p", "money": "<PERSON><PERSON> tiền thanh toán", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "tradeNo": "<PERSON><PERSON> đơn hàng", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createTime": "<PERSON><PERSON><PERSON><PERSON> gian tạo"}, "status": {"success": "<PERSON><PERSON><PERSON><PERSON> công", "pending": "<PERSON><PERSON> lý", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "paymentMethod": {"alipay": "Alipay", "wxpay": "WeChat", "USDT_TRC20": "USDT", "EVM_ETH_ETH": "ETH", "paypal": "PayPal"}}, "logContentDetail": {"description": "Thông tin mô tả", "downstreamError": "Lỗi downstream", "originalError": "Lỗi gốc", "requestParams": "<PERSON><PERSON> số yêu cầu", "copy": "Sao chép"}, "viewMode": {"switchTo": "<PERSON><PERSON><PERSON><PERSON> sang góc nhìn {{mode}}", "cost": "Chi phí", "usage": "<PERSON><PERSON><PERSON><PERSON> sử dụng"}, "agenciesTable": {"title": "<PERSON><PERSON><PERSON><PERSON> lý đại lý", "addAgency": "<PERSON><PERSON><PERSON><PERSON> đại lý", "columns": {"id": "ID", "userId": "ID người dùng", "name": "<PERSON><PERSON><PERSON>", "domain": "<PERSON><PERSON><PERSON>", "commissionRate": "Tỷ lệ hoa hồng", "salesVolume": "<PERSON><PERSON><PERSON> s<PERSON>", "userCount": "<PERSON><PERSON> ng<PERSON>ời dùng", "commissionIncome": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> hoa hồng", "historicalCommission": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> tích l<PERSON>y", "actions": "<PERSON><PERSON>"}, "confirm": {"deleteTitle": "Bạn có chắc chắn muốn xóa đại lý này không?", "updateName": "<PERSON><PERSON> cập nhật tên đại lý...", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công", "updateFailed": "<PERSON><PERSON><PERSON> nhật thất bại", "deleteSuccess": "<PERSON><PERSON>a thành công!"}, "messages": {"getListFailed": "<PERSON><PERSON><PERSON> danh sách đại lý thất bại: {{message}}", "deleteSuccess": "<PERSON><PERSON>a thành công!", "loadingData": "<PERSON><PERSON> tả<PERSON>..."}}, "units": {"times": "<PERSON><PERSON><PERSON>", "percentage": "{{value}}%", "formatUsage": "{{name}}: {{value}} lần ({{percent}}%)"}, "notification": {"title": "Cài đặt thông báo", "subscriptionEvents": "Sự kiện đăng ký", "notificationMethods": "<PERSON><PERSON><PERSON><PERSON> thức thông báo", "alertSettings": "<PERSON>ài đặt cảnh báo", "emailConfig": "<PERSON><PERSON><PERSON> h<PERSON>", "customEmails": "Đ<PERSON>a chỉ email tùy chỉnh", "addEmail": "Thêm email", "removeEmail": "Xóa", "emailPlaceholder": "<PERSON><PERSON> lòng nhập địa chỉ email", "emailTooltip": "<PERSON><PERSON><PERSON>hông đ<PERSON>, sẽ sử dụng email dự phòng của tài kho<PERSON>n", "emailDescription": "<PERSON><PERSON>u bạn muốn gửi thông báo đến địa chỉ email kh<PERSON><PERSON>, vui lòng cấu hình tại đây. <PERSON><PERSON> trống sẽ sử dụng địa chỉ email dự phòng của tài kho<PERSON>n.", "balanceThreshold": "Ngưỡng cảnh báo số dư", "balanceThresholdTooltip": "<PERSON><PERSON><PERSON> thông báo cảnh báo khi số dư tài kho<PERSON>n thấp hơn ngưỡng này", "balanceThresholdDescription": "<PERSON><PERSON><PERSON> thông bá<PERSON> cảnh báo khi số dư thấp hơn giá trị này (ki<PERSON><PERSON> tra thờ<PERSON> gian thực, tối đa 1 lần thông báo trong 2 giờ)", "alertExplanationTitle": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>ch cảnh b<PERSON>o", "alertExplanation": "• <PERSON><PERSON><PERSON> báo số dư: <PERSON><PERSON><PERSON> tra số dư người dùng thời gian thực, thông báo ngay khi thấp hơn ngưỡng\n• Thông báo tiếp thị: <PERSON><PERSON><PERSON> tra hàng ngày một lần, tr<PERSON><PERSON> làm phiền quá mức\n• <PERSON><PERSON><PERSON> báo bảo mật: Th<PERSON><PERSON> báo ngay khi xảy ra, đả<PERSON> bảo an toàn tài kho<PERSON>\n• Thô<PERSON> báo hệ thống: Thông báo một lần cho tất cả người dùng khi có cập nhật quan trọng", "selectEvents": "<PERSON><PERSON><PERSON> loại sự kiện bạn quan tâm", "eventsDescription": "<PERSON><PERSON> các sự kiện này x<PERSON> ra, hệ thống sẽ gửi thông báo cho bạn qua phương thức bạn đã chọn", "selectMethods": "<PERSON><PERSON><PERSON> cách thức nhận thông báo", "methodsDescription": "<PERSON><PERSON><PERSON> có thể bật nhi<PERSON>u phương thức thông báo cùng lúc, hệ thống sẽ gửi thông báo qua tất cả các phương thức đã bật", "description": "<PERSON><PERSON><PERSON><PERSON> lý tùy chọn thông báo của bạn, chọn nhận loại thông báo nào và qua phương thức nào", "recommended": "<PERSON><PERSON><PERSON><PERSON><PERSON> ngh<PERSON> bật", "important": "<PERSON><PERSON> tr<PERSON>", "testRecommendation": "<PERSON><PERSON><PERSON><PERSON>n nghị kiểm tra sau khi lưu cài đặt để đảm bảo chức năng thông báo hoạt động bình thường", "testNotification": "<PERSON><PERSON><PERSON> tra thông báo", "testMessage": "<PERSON><PERSON><PERSON> là tin nhắn thông báo kiểm tra", "testSuccess": "<PERSON><PERSON><PERSON> thông báo kiểm tra thành công", "testFailed": "<PERSON><PERSON><PERSON> thông báo kiểm tra thất bại", "saveSuccess": "<PERSON><PERSON><PERSON> cài đặt thông báo thành công", "saveFailed": "<PERSON><PERSON><PERSON> cài đặt thất bại", "validation": {"invalidEmail": "<PERSON><PERSON> lòng nhập địa chỉ email hợp lệ", "emailRequired": "Địa chỉ email không đ<PERSON><PERSON><PERSON> để trống", "invalidUrl": "<PERSON><PERSON> lòng nhập URL hợp lệ", "qywxWebhookRequired": "<PERSON><PERSON> lò<PERSON> nhập URL Webhook robot WeChat doanh nghiệp", "wxpusherTokenRequired": "<PERSON><PERSON> lòng nhập WxPusher APP Token", "wxpusherUidRequired": "<PERSON><PERSON> lòng nh<PERSON>p UID người dùng WxPusher", "dingtalkWebhookRequired": "<PERSON><PERSON> lòng nhập URL Webhook robot DingTalk", "feishuWebhookRequired": "<PERSON><PERSON> lòng nhập URL Webhook robot Feishu", "webhookUrlRequired": "<PERSON><PERSON> lòng nhập URL Webhook", "telegramTokenRequired": "<PERSON><PERSON> lòng nhập Telegram Bot Token", "telegramChatIdRequired": "<PERSON><PERSON> lòng nhập Telegram Chat ID", "telegramBotTokenRequired": "<PERSON><PERSON> lòng nhập Telegram Bot Token"}, "qywxbotConfig": "<PERSON><PERSON><PERSON> robot <PERSON><PERSON><PERSON> do<PERSON> nghi<PERSON>p", "qywxbotGuide": "Hướng dẫn cấu hình robot WeChat doanh nghiệp", "wxpusherConfig": "<PERSON><PERSON><PERSON> h<PERSON>", "wxpusherGuide": "<PERSON><PERSON><PERSON>ng dẫn cấu hình WxPusher", "wxpusherUid": "UID người dùng", "dingtalkConfig": "<PERSON><PERSON><PERSON> robot DingTalk", "dingtalkGuide": "Hướng dẫn cấu hình robot DingTalk", "feishuConfig": "<PERSON><PERSON><PERSON> h<PERSON>nh robot <PERSON><PERSON><PERSON>", "feishuGuide": "H<PERSON><PERSON>ng dẫn cấu hình robot <PERSON><PERSON>u", "webhookConfig": "<PERSON><PERSON><PERSON> <PERSON>", "webhookGuide": "Hướng dẫn cấu hình <PERSON>ok", "webhookUrl": "Địa chỉ gọi", "webhookToken": "<PERSON><PERSON><PERSON> chỉ giao di<PERSON>n", "webhookTokenTooltip": "<PERSON><PERSON>, dùng cho xác thực <PERSON>", "webhookTokenPlaceholder": "<PERSON><PERSON> (t<PERSON><PERSON>)", "telegramConfig": "<PERSON><PERSON><PERSON> h<PERSON>nh robot Telegram", "telegramGuide": "Hướng dẫn cấu hình robot Telegram", "telegramChatIdPlaceholder": "********* hoặc @username", "events": {"account_balance_low": "<PERSON><PERSON><PERSON> báo số dư không đủ", "account_quota_expiry": "<PERSON><PERSON><PERSON> mức sắp hết hạn", "security_alert": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON><PERSON> mật", "system_announcement": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống", "promotional_activity": "<PERSON><PERSON><PERSON><PERSON> báo hoạt động khu<PERSON>ến mãi", "model_pricing_update": "<PERSON><PERSON><PERSON> nhật giá mô hình", "anti_loss_contact": "<PERSON><PERSON><PERSON> mất liên lạc - <PERSON><PERSON><PERSON><PERSON> bá<PERSON> định kỳ"}, "eventDescriptions": {"account_balance_low": "<PERSON><PERSON><PERSON><PERSON> báo cho bạn nạp tiền kịp thời khi số dư tài khoản thấp hơn ngưỡng đã đặt", "account_quota_expiry": "<PERSON><PERSON><PERSON><PERSON> bá<PERSON> tr<PERSON><PERSON><PERSON> khi hạn mức tài kho<PERSON>n sắp hết hạn", "security_alert": "Nhắc nhở liên quan đến bảo mật như đăng nhập bất thườ<PERSON>, thay đổi mật khẩu", "system_announcement": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> hệ thống quan trọng, thông báo bảo trì và phát hành tính năng", "promotional_activity": "Hoạt động ưu đãi mới, thông tin giảm giá và khuyến mãi đặc biệt", "model_pricing_update": "Thô<PERSON> báo thay đổi giá mô hình và cập nhật quy tắc tính phí", "anti_loss_contact": "<PERSON><PERSON><PERSON> thông báo định kỳ để đảm bảo thông tin liên lạc có hiệu lực"}, "methodDescriptions": {"email": "<PERSON><PERSON><PERSON><PERSON> tin nhắn thông báo qua email", "telegram": "<PERSON><PERSON><PERSON><PERSON> thông b<PERSON>o qua robot Telegram", "webhook": "<PERSON><PERSON><PERSON><PERSON> thông báo qua <PERSON>hook", "wxpusher": "<PERSON><PERSON> d<PERSON><PERSON> v<PERSON> đ<PERSON><PERSON>Chat WxPusher", "qywxbot": "<PERSON><PERSON><PERSON><PERSON> thông báo qua robot We<PERSON><PERSON> doanh nghiệp", "dingtalk": "<PERSON><PERSON><PERSON><PERSON> thông b<PERSON><PERSON> qua robot DingTalk", "feishu": "<PERSON><PERSON><PERSON><PERSON> thông b<PERSON>o qua <PERSON> <PERSON><PERSON><PERSON>"}, "methods": {"email": "Thông báo email", "telegram": "Thông báo Telegram", "webhook": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "wxpusher": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> Wx<PERSON><PERSON>er", "qywxbot": "Robot WeChat do<PERSON>h nghi<PERSON>", "dingtalk": "Robot DingTalk", "feishu": "Robot Feishu"}, "configurationSteps": "配置步骤：", "detailedDocumentation": "详细文档：", "qywxbotConfigurationGuide": "企业微信机器人配置指南", "qywxbotStep1": "在企业微信群聊中，点击右上角「...」→「群机器人」", "qywxbotStep2": "点击「添加机器人」→「自定义机器人」", "qywxbotStep3": "设置机器人名称和头像，完成创建", "qywxbotStep4": "复制生成的Webhook URL（格式：https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx）", "qywxbotStep5": "将Webhook URL填入上方配置项", "qywxbotDocumentationLink": "企业微信群机器人配置说明", "wxpusherConfiguration": "WxPusher配置", "wxpusherConfigurationGuide": "WxPusher配置指南", "wxpusherAppToken": "APP Token", "wxpusherUserUID": "用户UID", "wxpusherAppTokenPlaceholder": "AT_xxx", "wxpusherUserUIDPlaceholder": "UID_xxx", "wxpusherAppTokenRequired": "请输入WxPusher APP Token", "wxpusherUserUIDRequired": "请输入WxPusher用户UID", "wxpusherStep1": "访问 WxPusher官网 注册账号", "wxpusherStep2": "创建应用获取APP Token（格式：AT_xxx）", "wxpusherStep3": "微信扫码关注应用二维码获取用户UID（格式：UID_xxx）", "wxpusherStep4": "将APP Token和用户UID填入上方配置项", "wxpusherOfficialWebsite": "WxPusher官网", "dingtalkConfigurationGuide": "钉钉机器人配置指南", "dingtalkStep1": "在钉钉群聊中，点击右上角「...」→「群助手」→「添加机器人」", "dingtalkStep2": "选择「自定义」机器人，点击「添加」", "dingtalkStep3": "设置机器人名称和头像，选择安全设置：", "dingtalkStep4": "完成创建后，复制生成的Webhook URL", "dingtalkStep5": "URL格式：https://oapi.dingtalk.com/robot/send?access_token=xxx", "dingtalkSecurityNote": "• 建议启用加签验证，提高安全性", "dingtalkPrivacyNote": "• 请妥善保管Webhook URL，避免泄露", "dingtalkDocumentationLink": "钉钉自定义机器人接入", "feishuConfigurationGuide": "飞书机器人配置指南", "feishuStep1": "在飞书群聊中，点击右上角「设置」→「群机器人」", "feishuStep2": "点击「添加机器人」→「自定义机器人」", "feishuStep3": "设置机器人名称和描述", "feishuStep4": "选择安全设置（推荐启用签名校验）", "feishuStep5": "复制生成的Webhook URL", "feishuStep6": "如启用签名校验，同时复制签名密钥", "feishuStep7": "将Webhook URL和签名密钥填入上方配置项", "feishuSecurityNote": "• 建议启用签名校验，提高安全性", "feishuPrivacyNote": "• 请妥善保管Webhook URL，避免泄露", "feishuDocumentationLink": "飞书自定义机器人接入", "telegramConfigurationGuide": "Telegram机器人配置指南", "telegramStep1": "在Telegram中搜索并添加 @BotFather 机器人", "telegramStep2": "发送 /newbot 命令创建新机器人", "telegramStep3": "按提示设置机器人名称和用户名", "telegramStep4": "复制获得的Bot Token", "telegramStep5": "向您的机器人发送任意消息", "telegramStep6": "访问 https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates", "telegramStep7": "从返回结果中找到您的Chat ID", "telegramStep8": "将Bot Token和Chat ID填入上方配置项", "telegramSecurityNote": "• 请妥善保管Bot Token，避免泄露", "telegramDocumentationLink": "Telegram Bot API 官方文档", "dingtalkStep3a": "• 推荐选择「自定义关键词」，添加关键词如「通知」、「预警」等", "dingtalkStep3b": "• 或选择「加签」方式（需要额外配置签名）", "dingtalkStep6": "将Webhook URL填入上方配置项", "dingtalkNoticeTitle": "注意事项：", "dingtalkRateLimit": "• 每个机器人每分钟最多发送20条消息", "dingtalkKeywordNote": "• 如果设置了关键词安全设置，消息内容必须包含设置的关键词", "feishuStep3Detailed": "设置机器人名称、描述和头像", "feishuStep4Detailed": "选择安全设置（推荐使用签名校验或关键词验证）：", "feishuSignatureVerification": "• 签名校验：提供更高的安全性，需要配置密钥", "feishuKeywordVerification": "• 关键词验证：消息必须包含指定关键词，如「通知」、「预警」", "feishuStep5Detailed": "完成创建后，复制生成的Webhook URL", "feishuStep6Detailed": "URL格式：https://open.feishu.cn/open-apis/bot/v2/hook/xxx", "feishuStep7Detailed": "将Webhook URL填入上方配置项", "feishuMessageFormatsTitle": "消息格式支持：", "feishuTextMessage": "• 文本消息：支持纯文本和@功能", "feishuRichTextMessage": "• 富文本消息：支持markdown格式、链接、图片等", "feishuCardMessage": "• 消息卡片：支持交互式卡片消息", "feishuImageMessage": "• 图片消息：支持发送图片内容", "feishuNoticeTitle": "注意事项：", "feishuRateLimit": "• 每个机器人每分钟最多发送100条消息", "feishuKeywordNote": "• 如果设置了关键词验证，消息内容必须包含设置的关键词", "feishuSecurityRecommendation": "• 建议启用签名校验以提高安全性", "telegramStep6Detailed": "在返回的JSON中找到 chat.id 字段的值", "telegramStep7Detailed": "将Bot Token和Chat ID配置到您的账户设置中", "telegramStep8Detailed": "将Bot Token和Chat ID填入上方配置项", "telegramNoticeTitle": "注意事项：", "telegramRateLimit": "• 每个机器人每秒最多发送30条消息", "telegramMessageFormats": "• 支持文本、图片、文档等多种消息格式", "telegramMarkdownSupport": "• 支持Markdown和HTML格式的富文本消息", "telegramInlineKeyboard": "• 支持内联键盘和自定义键盘", "telegramPrivacyNote": "• 请妥善保管Bot Token，避免泄露", "telegramSecurityTip": "• 建议定期更换Bot Token以提高安全性", "telegramStep4Detailed": "创建成功后，BotFather会返回Bot Token（格式：*********0:ABCDEFGHIJKLMNOPQRSTUVWXYZ）", "telegramStep5Detailed": "获取Chat ID的方法：", "telegramPersonalChat": "• 个人聊天：向机器人发送消息，然后访问", "telegramGroupChat": "• 群组聊天：将机器人添加到群组，发送消息后同样访问上述链接", "telegramChannel": "• 频道：将机器人添加为管理员，Chat ID通常以-100开头", "telegramChatIdFormatsTitle": "Chat ID格式说明：", "telegramPersonalChatId": "• 个人聊天：正整数，如 *********", "telegramGroupChatId": "• 群组：负整数，如 -987654321", "telegramSuperGroupChatId": "• 超级群组/频道：以-100开头，如 -100*********0", "telegramUsernameFormat": "• 用户名：也可以使用@username格式（仅限公开群组/频道）", "telegramInteractionRequired": "• 机器人只能向已经与其交互过的用户发送消息", "telegramGroupMembership": "• 群组中需要先将机器人添加为成员", "telegramChannelPermission": "• 频道中机器人需要有发送消息的权限", "webhookCallUrl": "调用地址", "webhookConfigurationGuide": "Webhook配置指南", "webhookDataFormatExample": "数据格式示例：", "webhookConfigurationInstructions": "配置说明：", "webhookRequestMethod": "• 请求方法：POST", "webhookContentType": "• Content-Type: application/json", "webhookAuthMethod": "• 认证方式：Bearer <PERSON>ken（可选，填写后会在请求头中添加 Authorization: Bearer {token}）", "webhookTimeout": "• 超时时间：30秒", "webhookRetryMechanism": "• 重试机制：失败后会重试2次", "webhookTip": "💡 提示：确保您的Webhook端点能够接收POST请求并返回2xx状态码", "telegramStep3Detailed": "按提示设置机器人名称和用户名（用户名必须以bot结尾）", "telegramPersonalChatDetailed": "• 个人聊天：向机器人发送消息，然后访问", "telegramGroupChatDetailed": "• 群组聊天：将机器人添加到群组，发送消息后同样访问上述链接", "telegramChannelDetailed": "• 频道：将机器人添加为管理员，Chat ID通常以-100开头", "telegramQuickChatIdTitle": "快速获取Chat ID示例：", "telegramQuickStep1": "替换BOT_TOKEN：https://api.telegram.org/bot YOUR_BOT_TOKEN /getUpdates", "telegramQuickStep2": "在浏览器中访问上述链接", "telegramQuickStep3": "在JSON响应中查找：\"chat\":{\"id\":*********}"}, "dailyUsage": {"total": "总计", "totalCost": "总成本", "tooltipTitle": {"cost": "成本情况", "usage": "使用情况"}, "yAxisName": {"cost": "成本 (USD)", "usage": "使用量 (USD)"}}, "dailyUsageByModel": {"total": "总计", "tooltipTotal": "总计: $ {{value}}", "switchTo": "切换到", "cost": "成本", "usage": "使用量", "perspective": "视角", "granularity": {"hour": "按小时", "day": "按天", "week": "按周", "month": "按月"}}, "checkinModal": {"title": "请完成验证", "captchaPlaceholder": "验证码", "confirm": "确定", "close": "关闭"}, "balanceTransfer": {"title": "账户间转账", "accountInfo": {"balance": "账户余额", "transferFee": "转账手续费", "groupNote": "仅相同的用户组之间可转账"}, "form": {"receiverId": "接收者ID", "receiverUsername": "接收者用户名", "remark": "备注信息", "amount": "转账金额", "expectedFee": "预计扣费", "submit": "发起转账"}, "result": {"success": "转账成功", "continueTransfer": "继续转账", "viewRecord": "查看记录"}, "warning": {"disabled": "管理员未开启转账功能，暂时无法使用"}, "placeholder": {"autoCalculate": "填写转账金额自动计算"}}, "channelsTable": {"title": "<PERSON><PERSON><PERSON><PERSON> lý k<PERSON>nh", "columns": {"id": "ID", "name": "<PERSON><PERSON><PERSON>", "createdTime": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "type": "<PERSON><PERSON><PERSON>", "key": "Khóa", "base": "<PERSON><PERSON><PERSON> chỉ giao di<PERSON>n", "models": "<PERSON><PERSON>", "weight": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "priority": "Ưu tiên", "retryInterval": "<PERSON><PERSON><PERSON><PERSON> thời gian thử lại", "responseTime": "<PERSON><PERSON><PERSON><PERSON> gian ph<PERSON>n hồi", "rpm": "RPM", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "quota": "Số dư", "expireTime": "<PERSON><PERSON><PERSON><PERSON> gian hế<PERSON> hạn", "group": "Nhóm", "billingType": "Loại t<PERSON>h phí", "actions": "<PERSON><PERSON>", "fusing": "<PERSON><PERSON><PERSON>", "sort": "Ưu tiên", "disableReason": "Lý do vô hiệu hóa"}, "status": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "normal": "<PERSON><PERSON><PERSON>", "enabled": "<PERSON>r<PERSON><PERSON> thái bình thường", "manualDisabled": "<PERSON><PERSON> hiệu hóa thủ công", "waitingRetry": "Chờ khởi động lại", "suspended": "<PERSON><PERSON><PERSON> ngừng sử dụng", "partiallyDisabled": "<PERSON><PERSON> hi<PERSON>u hóa một phần", "specified": "Tr<PERSON>ng thái chỉ định", "allDisabled": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "specifiedDisabled": "Loại vô hiệu hóa chỉ định"}, "placeholder": {"selectGroup": "<PERSON><PERSON> lòng chọn/tìm kiếm nhóm", "selectStatus": "<PERSON><PERSON><PERSON> trạng thái kênh", "inputSelectModel": "<PERSON><PERSON><PERSON><PERSON>/chọn tên mô hình", "selectFusingStatus": "<PERSON><PERSON><PERSON> trạng thái ngắt mạch tự động"}, "quota": {"usageAmount": "<PERSON><PERSON> tiêu thụ: {amount}", "remainingAmount": "Còn lại: {amount}", "customTotalAmount": "Tổng số tùy chỉnh: {amount}", "updateNotSupported": "<PERSON><PERSON><PERSON> thời không hỗ trợ cập nhật số dư, vui lòng sử dụng số dư tùy chỉnh", "details": "<PERSON> ti<PERSON>", "sufficient": "Đủ"}, "actions": {"edit": "Chỉnh sửa", "copy": "<PERSON><PERSON> ch<PERSON><PERSON> kênh", "delete": "<PERSON><PERSON><PERSON>", "enable": "<PERSON><PERSON><PERSON>", "disable": "Tắt", "test": "<PERSON><PERSON><PERSON> tra", "advancedTest": "<PERSON><PERSON><PERSON> tra nâng cao", "viewLog": "<PERSON><PERSON><PERSON><PERSON> ký kênh", "viewAbility": "<PERSON><PERSON> n<PERSON>ng", "cleanUsage": "<PERSON>óa đã sử dụng", "updateBalance": "<PERSON><PERSON><PERSON> nh<PERSON>t số dư", "copyKey": "Sao chép khóa"}, "confirm": {"deleteTitle": "<PERSON><PERSON><PERSON>n x<PERSON>a", "deleteContent": "<PERSON>ạn có chắc chắn muốn xóa kênh {{name}} (#{{id}}) không?", "cleanUsageTitle": "<PERSON><PERSON><PERSON>n x<PERSON><PERSON> lư<PERSON>ng sử dụng", "cleanUsageContent": "Bạn có chắc chắn muốn xóa số tiền đã tiêu thụ của kênh {{name}} (#{{id}}) không?", "testTitle": "<PERSON><PERSON><PERSON> n<PERSON>n kiểm tra", "testContent": "Bạn có chắc chắn muốn kiểm tra kênh {{status}} không?", "testNote": "Lưu ý: <PERSON><PERSON><PERSON> năng này cần phối hợp với [<PERSON><PERSON><PERSON> hình]->[<PERSON>y<PERSON><PERSON> tiếp]->[Cài đặt giám sát]->[Vô hiệu hóa kênh khi thất bại, bật kênh khi thành công] để sử dụng. Nếu không bật cài đặt liên quan, sau khi kiểm tra hoàn thành sẽ không tự động vô hiệu hóa hoặc bật kênh.", "deleteDisabledTitle": "<PERSON><PERSON><PERSON>n x<PERSON>a", "deleteDisabledContent": "Bạn có chắc chắn muốn xóa tất cả kênh {{type}} không?"}, "messages": {"operationSuccess": "<PERSON><PERSON> tác thành công", "operationSuccessWithSort": "<PERSON><PERSON> tá<PERSON> thành công, thứ tự kênh có thể thay đổi, <PERSON><PERSON><PERSON><PERSON><PERSON> nghị sắp xếp theo ID!", "operationFailed": "<PERSON><PERSON> tác thất bại: {{message}}", "testRunning": "<PERSON><PERSON><PERSON> {{name}}(#{{id}}) đang chạy kiểm tra, vui lòng đợi...", "testSuccess": "<PERSON><PERSON><PERSON> \"{{name}}(#{{id}})\" {{model}} kiểm tra thành công, thời gian ph<PERSON>n hồi {{time}} gi<PERSON>y", "testSuccessWithWarnings": "<PERSON><PERSON><PERSON> \"{{name}}(#{{id}})\" {{model}} kiểm tra hoàn thành, thời gian phản hồi {{time}} g<PERSON><PERSON><PERSON>, nhưng có thông tin cảnh báo", "viewDetails": "<PERSON>em chi tiết", "testFailed": "<PERSON><PERSON><PERSON> \"{{name}}(#{{id}})\" {{model}} kiểm tra thất bại. M<PERSON> trạng thái: {{code}}, lý do: {{reason}}, nhấp để xem chi tiết", "testStarted": "Bắt đầu kiểm tra kênh {{status}}, vui lòng làm mới sau để xem kết quả. Việc áp dụng kết quả kiểm tra phụ thuộc vào cài đặt giám sát của bạn.", "testOperationFailed": "<PERSON><PERSON><PERSON> tra thất b<PERSON>i", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công {{count}} kênh", "deleteFailed": "<PERSON><PERSON><PERSON> thất bại: {{message}}", "modelPrefix": "<PERSON><PERSON> h<PERSON> {{model}} ", "channelInfo": "Thông tin kênh", "channelDetail": "{{name}}(#{{id}}){{modelInfo}}", "updateBalanceSuccess": "<PERSON><PERSON><PERSON> nhật số dư kênh \"{{name}}\" thành công", "updateBalanceFailed": "<PERSON><PERSON><PERSON> nh<PERSON>t số dư kênh \"{{name}}\" thất bại: {{message}}", "updateAllBalanceStarted": "<PERSON><PERSON><PERSON> đầu cập nhật số dư tất cả kênh trạng thái bình thường", "updateAllBalanceSuccess": "<PERSON><PERSON><PERSON> nhật số dư tất cả kênh thành công", "fetchGroupError": "获取渠道组数据出错：{{response}}", "fetchChannelError": "获取渠道数据失败：{{message}}", "selectChannelFirst": "请先选择要删除的渠道", "deleteDisabledSuccess": "已删除所有{{type}}渠道，共 {{count}} 个", "deleteOperationFailed": "删除失败", "copySuccess": "复制成功", "copyFailed": "复制失败：{{message}}", "emptyKey": "密钥为空", "fetchChannelDetailError": "获取渠道详情失败：{{message}}", "topupSuccess": "充值成功", "topupFailed": "充值失败：{{message}}"}, "popover": {"channelInfo": "渠道信息"}, "menu": {"deleteManualDisabled": "删除手动禁用渠道", "deleteWaitingRetry": "删除等待重启渠道", "deleteSuspended": "删除暂停使用渠道", "deleteDisabledAccount": "删除停用账号", "deleteQuotaExceeded": "删除配额超限渠道", "deleteRateLimitExceeded": "删除频率限制渠道", "deleteInvalidKey": "删除无效密钥渠道", "deleteConnectionError": "删除连接错误渠道", "testAll": "测试所有渠道", "testNormal": "测试正常渠道", "testManualDisabled": "测试手动禁用渠道", "testWaitingRetry": "测试等待重启渠道", "testSuspended": "测试暂停使用渠道"}, "tooltip": {"testNote": "需要配合[配置]->[中继]->[监控设置]->[失败时禁用渠道,成功时启用通道]来使用。如果没有开启则不会在测速完成后自动禁用或者启用。"}, "disableReasons": {"account_deactivated": "账号停用", "quota_exceeded": "配额超限", "rate_limit_exceeded": "频率限制", "invalid_key": "无效密钥", "connection_error": "连接错误"}, "topup": {"reminder1": "上游充值成功后，请点击更新余额按钮刷新余额", "reminder2": "如充值失败，请检查兑换码是否正确或联系管理员"}}, "billingTypes": {"quota": "额度", "times": "次数"}, "serverLogViewer": {"title": "<PERSON><PERSON><PERSON><PERSON> xem nhật ký máy chủ", "connecting": "<PERSON><PERSON> kết n<PERSON>i máy chủ...", "downloadSelect": "<PERSON><PERSON><PERSON> tệp nhật ký để tải xuống", "nginxConfig": "Hướng dẫn cấu hình <PERSON>x WebSocket", "directAccess": "Nếu sử dụng tên miền để truy cập và chưa cấu hình hỗ trợ WebSocket, trình xem nhật ký sẽ không hoạt động. <PERSON><PERSON><PERSON> này bạn có thể truy cập trực tiếp qua IP và cổng máy chủ (ví dụ: http://your-ip:9527).", "domainAccess": "<PERSON><PERSON> truy cập qua tên miền, cần thêm cấu hình sau vào cấu hình Nginx để hỗ trợ WebSocket:", "buttons": {"pause": "<PERSON><PERSON><PERSON>", "resume": "<PERSON><PERSON><PERSON><PERSON>", "clear": "Xóa"}, "errors": {"fetchFailed": "<PERSON><PERSON><PERSON> danh sách tệp nhật ký thất bại", "downloadFailed": "<PERSON><PERSON><PERSON> xuống tệp nhật ký thất bại", "wsError": "Lỗi kết nối WebSocket"}}, "channelScore": {"score": "<PERSON><PERSON><PERSON><PERSON>", "successRate": "Tỷ lệ thành công", "avgResponseTime": "<PERSON>h<PERSON><PERSON> gian phản hồi trung bình", "title": "<PERSON><PERSON><PERSON><PERSON> số kênh", "hourlyTitle": "<PERSON><PERSON><PERSON><PERSON> số kênh theo giờ", "dailyTitle": "<PERSON><PERSON><PERSON><PERSON> số kênh theo ng<PERSON>y", "weeklyTitle": "<PERSON><PERSON><PERSON><PERSON> số kênh theo tu<PERSON>n", "monthlyTitle": "<PERSON><PERSON><PERSON><PERSON> số kênh theo tháng", "allTimeTitle": "<PERSON><PERSON><PERSON><PERSON> số tổng thể kênh", "infoTooltip": "Điểm số kênh là điểm đánh giá tổng hợp đượ<PERSON> tính dựa trên tỷ lệ thành công và thời gian phản hồi", "tableView": "<PERSON><PERSON> độ xem bảng", "chartView": "<PERSON>ế độ xem biểu đồ", "refresh": "<PERSON><PERSON><PERSON>", "selectModel": "<PERSON><PERSON><PERSON> mô hình", "allModels": "<PERSON><PERSON><PERSON> cả mô hình", "sortByScore": "<PERSON><PERSON><PERSON> xếp theo điểm số", "sortBySuccessRate": "<PERSON><PERSON><PERSON> xếp theo tỷ lệ thành công", "sortByResponseTime": "<PERSON><PERSON><PERSON> xếp theo thời gian phản hồi", "noData": "Chưa có dữ liệu", "totalItems": "Tổng cộng {{total}} mục", "fetchError": "<PERSON><PERSON><PERSON> dữ liệu điểm số kênh thất bại", "aboutScoring": "<PERSON><PERSON> t<PERSON>h toán điểm số", "scoringExplanation": "Điểm số kênh là điểm đánh giá tổng hợp đư<PERSON><PERSON> tính dựa trên tỷ lệ thành công và thời gian <PERSON>h<PERSON> hồi, điểm tối đa là 1 điểm.", "successRateWeight": "<PERSON>r<PERSON><PERSON> số tỷ lệ thành công (70%)", "successRateExplanation": "Tỷ lệ thành công càng cao, điể<PERSON> số càng cao", "responseTimeWeight": "<PERSON><PERSON><PERSON><PERSON> số thời gian p<PERSON><PERSON>n hồ<PERSON> (30%)", "responseTimeExplanation": "Thời gian phản hồi dưới 1000ms được điểm tối đa, vư<PERSON><PERSON> quá sẽ bị trừ điểm theo tỷ lệ", "columns": {"rank": "<PERSON><PERSON><PERSON> h<PERSON>", "channelId": "ID kênh", "channelName": "<PERSON><PERSON><PERSON> k<PERSON>", "model": "<PERSON><PERSON>", "totalRequests": "<PERSON><PERSON>ng số yêu cầu", "successRequests": "<PERSON><PERSON><PERSON> c<PERSON>u thành công", "failedRequests": "<PERSON><PERSON><PERSON> c<PERSON>u thất bại", "successRate": "Tỷ lệ thành công", "avgResponseTime": "<PERSON>h<PERSON><PERSON> gian phản hồi trung bình", "score": "<PERSON><PERSON><PERSON><PERSON> tổng hợp", "actions": "<PERSON><PERSON>"}, "actions": {"viewDetails": "<PERSON>em chi tiết", "test": "<PERSON><PERSON><PERSON> tra kênh", "edit": "Chỉnh sửa kênh"}, "tooltips": {"excellent": "<PERSON><PERSON><PERSON>", "good": "<PERSON><PERSON><PERSON>", "average": "<PERSON>rung bình", "poor": "<PERSON><PERSON><PERSON>", "veryPoor": "<PERSON><PERSON><PERSON>"}, "scoringExplanation100": "Điểm số kênh là điểm đánh giá tổng hợp đư<PERSON><PERSON> tính dựa trên tỷ lệ thành công và thời gian <PERSON>h<PERSON> hồi, điểm tối đa là 100 điểm."}, "menu": {"channelScores": "<PERSON><PERSON><PERSON><PERSON> số kênh"}, "relay": {"dispatchOptions": "调度选项", "preciseWeightCalculation": "权重精确计算", "preciseWeightCalculationTip": "启用后将使用更精确的算法计算渠道权重，可能会增加CPU开销", "channelMetricsEnabled": "启用渠道指标统计", "channelMetricsEnabledTip": "开启后会收集渠道的成功率、响应时间等指标，用于评估渠道性能。关闭则不会收集这些数据，可减少系统资源占用。", "channelScoreRoutingEnabled": "启用基于渠道得分的智能路由", "channelScoreRoutingEnabledTip": "开启后系统会根据渠道的历史表现自动调整请求分配优先级。性能更好的渠道将获得更高的请求分配概率。", "globalIgnoreBillingTypeFilteringEnabled": "全局忽略计费方式筛选", "globalIgnoreBillingTypeFilteringEnabledTip": "开启后将忽略按量计费与按次计费的区分，不再根据计费方式筛选渠道，降低CPU和内存占用。", "globalIgnoreFunctionCallFilteringEnabled": "全局忽略函数调用筛选", "globalIgnoreFunctionCallFilteringEnabledTip": "开启后将忽略函数调用(Function Call)能力的筛选，不再专门筛选支持函数调用的渠道，降低资源占用。", "globalIgnoreImageSupportFilteringEnabled": "全局忽略图片支持筛选", "globalIgnoreImageSupportFilteringEnabledTip": "开启后将忽略图片支持能力的筛选，不再专门筛选支持图片输入的渠道，降低资源占用。"}, "dynamicRouter": {"title": "<PERSON><PERSON><PERSON><PERSON> lý định tuyến động", "reloadRoutes": "<PERSON><PERSON><PERSON> lại đ<PERSON>nh tuy<PERSON>n", "exportConfig": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "clearConfig": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "importantNotice": "<PERSON><PERSON><PERSON><PERSON> báo quan trọng", "reloadLimitation": "1. <PERSON><PERSON><PERSON> lại định tuyến chỉ có thể cập nhật cấu hình của các định tuyến hiện có, không thể thêm hoặc xóa định tuyến. Nếu cần tải lại hoàn toàn cấu trúc định tuyến, vui lòng khởi động lại ứng dụng.", "exportDescription": "2. <PERSON><PERSON><PERSON> cấu hình sẽ xuất cấu hình hiện tại trong cơ sở dữ liệu ra tệp router.j<PERSON>, lọc bỏ các giá trị rỗng và giá trị không.", "clearDescription": "3. <PERSON><PERSON><PERSON> cấu hình sẽ xóa tất cả cấu hình định tuyến động trong cơ sở dữ liệu, sau khi khởi động lại ứng dụng sẽ tải lại từ tệp router.json.", "routeGroups": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> t<PERSON>n", "upstreamConfig": "C<PERSON><PERSON> hình upstream", "endpointConfig": "<PERSON><PERSON><PERSON> h<PERSON> endpoint", "editRouteGroup": "Chỉnh sửa nhóm định tuyến", "editUpstream": "Chỉnh sửa cấu hình upstream", "editEndpoint": "Chỉnh s<PERSON>a cấu hình endpoint", "editJSON": "Chỉnh sửa JSON", "confirmClear": "<PERSON><PERSON><PERSON> n<PERSON>n x<PERSON>a cấu hình", "confirmClearMessage": "<PERSON><PERSON> tác này sẽ xóa tất cả cấu hình định tuyến động trong cơ sở dữ liệu, lần khởi động lại ứng dụng tiếp theo sẽ tải lại từ tệp cấu hình. Bạn có chắc chắn muốn tiếp tục không?", "configCleared": "<PERSON><PERSON>u hình định tuyến động đã đư<PERSON><PERSON>, vui lòng khởi động lại ứng dụng để áp dụng thay đổi", "configExported": "<PERSON><PERSON>u hình đã đư<PERSON><PERSON> xuất thành công ra tệp", "configReloaded": "<PERSON><PERSON><PERSON> hình định tuyến đã đư<PERSON><PERSON> tải lại thành công"}, "legal": {"privacyPolicy": {"title": "隐私政策", "lastUpdated": "最后更新日期：{{date}}", "sections": {"informationCollection": {"title": "信息收集", "description": "我们收集以下类型的信息：", "items": {"accountInfo": "账户信息：当您通过 Google 登录时，我们收集您的姓名、电子邮件地址和基本个人资料信息", "usageData": "使用数据：API 调用记录、使用统计和系统日志", "technicalInfo": "技术信息：IP 地址、浏览器类型、设备信息"}}, "informationUsage": {"title": "信息使用", "description": "我们使用收集的信息用于：", "items": ["提供和维护我们的服务", "用户身份验证和账户管理", "改进服务质量和用户体验", "发送重要的服务通知", "防止欺诈和滥用"]}, "informationSharing": {"title": "信息共享", "description": "我们不会出售、交易或转让您的个人信息给第三方，除非：", "items": ["获得您的明确同意", "法律要求或法院命令", "保护我们的权利、财产或安全"]}, "dataSecurity": {"title": "数据安全", "description": "我们采取适当的安全措施保护您的个人信息：", "items": ["数据加密传输和存储", "访问控制和权限管理", "定期安全审计和更新", "员工隐私培训"]}, "dataRetention": {"title": "数据保留", "description": "我们仅在必要期间保留您的个人信息：", "items": ["账户信息：账户存续期间", "使用日志：90天", "系统日志：30天"]}, "userRights": {"title": "您的权利", "description": "您有权：", "items": ["访问和更新您的个人信息", "删除您的账户和相关数据", "撤回同意", "数据可携带性"]}, "cookieUsage": {"title": "<PERSON><PERSON> 使用", "description": "我们使用 Cookie 和类似技术来：", "items": ["维持用户会话", "记住用户偏好", "分析网站使用情况"]}, "thirdPartyServices": {"title": "第三方服务", "description": "我们的服务可能包含第三方链接或集成：", "items": ["Google OAuth：用于用户身份验证", "GitHub OAuth：用于用户身份验证", "这些服务有自己的隐私政策"]}, "childrenPrivacy": {"title": "儿童隐私", "description": "我们的服务不面向13岁以下儿童。我们不会故意收集儿童的个人信息。"}, "policyUpdates": {"title": "政策更新", "description": "我们可能会更新此隐私政策。重大变更将通过电子邮件或网站通知您。"}, "contactUs": {"title": "联系我们", "description": "如果您对此隐私政策有任何问题，请联系我们：", "email": "邮箱", "address": "地址"}}}, "termsOfService": {"title": "服务条款", "lastUpdated": "最后更新日期：{{date}}", "importantNotice": "使用我们的服务即表示您同意这些条款。请仔细阅读。", "sections": {"serviceDescription": {"title": "服务描述", "description": "Shell API Pro Max 是一个 API 管理和代理服务平台，提供：", "items": ["API 密钥管理", "API 调用代理和转发", "使用统计和监控", "用户账户管理", "相关技术支持服务"]}, "userAccount": {"title": "用户账户", "description": "使用我们的服务需要：", "items": ["通过 Google 或 GitHub 进行身份验证", "提供准确、完整的注册信息", "保护账户安全，不与他人共享", "及时更新账户信息", "对账户下的所有活动负责"]}, "usageRules": {"title": "使用规则", "description": "您同意：", "items": ["合法使用：仅用于合法目的", "不滥用：不进行恶意攻击或过度请求", "不侵权：不侵犯他人知识产权", "不传播有害内容：不传播病毒、恶意软件", "遵守限制：遵守 API 调用限制和配额"]}, "prohibitedBehavior": {"title": "禁止行为", "description": "以下行为被严格禁止：", "items": ["尝试未经授权访问系统", "干扰或破坏服务正常运行", "逆向工程、反编译服务", "创建虚假账户或身份", "违反任何适用法律法规"]}, "serviceAvailability": {"title": "服务可用性", "description": "我们努力提供稳定的服务，但：", "items": ["不保证 100% 的服务可用性", "可能因维护、升级而暂停服务", "可能因不可抗力而中断服务", "会提前通知计划内的维护"]}, "feesAndPayment": {"title": "费用和付款", "description": "关于服务费用：", "items": ["基础服务可能免费提供", "高级功能可能需要付费", "费用标准在网站上公布", "付款后不可退款（法律要求除外）"]}, "intellectualProperty": {"title": "知识产权", "description": "关于知识产权：", "items": ["服务及其内容受知识产权法保护", "您获得有限的使用许可", "不得复制、修改、分发我们的内容", "您保留对自己数据的权利"]}, "privacyProtection": {"title": "隐私保护", "description": "我们重视您的隐私：", "items": ["按照隐私政策处理您的信息", "采取合理措施保护数据安全", "不会未经同意分享您的信息"]}, "disclaimer": {"title": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> miễn trừ trách n<PERSON>m", "description": "Trong phạm vi pháp luật cho phép:", "items": ["<PERSON><PERSON><PERSON> v<PERSON> đ<PERSON> cung cấp \"như hiện tại\"", "<PERSON>hông đảm bảo dịch vụ không có lỗi hoặc gián đo<PERSON>n", "<PERSON><PERSON><PERSON><PERSON> chịu tr<PERSON>ch nhi<PERSON> về thiệt hại gián tiếp", "<PERSON><PERSON><PERSON><PERSON> nhiệm giới hạn trong phạm vi phí bạn đã thanh toán"]}, "serviceTermination": {"title": "<PERSON><PERSON><PERSON> d<PERSON> d<PERSON> vụ", "description": "<PERSON><PERSON><PERSON> vụ có thể bị chấm dứt trong các trư<PERSON><PERSON> hợp sau:", "items": ["Bạn vi phạm các điều kho<PERSON>n này", "<PERSON><PERSON><PERSON> yêu cầu xóa tài k<PERSON>n", "<PERSON><PERSON>g tôi ngừng cung cấp dịch vụ", "<PERSON><PERSON><PERSON> c<PERSON>u p<PERSON> lý"]}, "termsModification": {"title": "<PERSON><PERSON>a đổi điều <PERSON>n", "description": "<PERSON><PERSON>g tôi có thể sửa đổi các điều kho<PERSON>n này:", "items": ["Thay đổi quan trọng sẽ được thông báo trước", "<PERSON><PERSON><PERSON><PERSON> tục sử dụng dịch vụ có nghĩa là chấp nhận điều khoản mới", "<PERSON><PERSON><PERSON> không đồng <PERSON>, vui lòng ngừng sử dụng dịch vụ"]}, "disputeResolution": {"title": "<PERSON><PERSON><PERSON><PERSON> quyết tranh chấp", "description": "<PERSON><PERSON><PERSON> có tranh chấp:", "items": ["<PERSON><PERSON><PERSON><PERSON><PERSON> tiên cố gắng thương lư<PERSON><PERSON> thân thiện", "<PERSON><PERSON> dụng pháp luật <PERSON>ộng hòa Nhân dân Trung Hoa", "<PERSON><PERSON><PERSON><PERSON> thẩm quyền của tòa án nơi cung cấp dịch vụ"]}, "contactUs": {"title": "<PERSON><PERSON><PERSON> h<PERSON> chúng tôi", "description": "<PERSON><PERSON>u bạn có bất kỳ câu hỏi nào về các điều khoản này, vui lòng liên hệ với chúng tôi:", "email": "Email", "address": "Địa chỉ", "serviceHours": "Giờ hỗ trợ: <PERSON><PERSON><PERSON>à<PERSON> việc 9:00-18:00"}}}, "common": {"copyright": "© {{year}} Shell API Pro Max. B<PERSON>o lưu mọi quyền.", "contactEmail": "<EMAIL>", "supportEmail": "<EMAIL>", "companyAddress": "[Địa chỉ công ty của bạn]"}}, "tasks": {"title": "<PERSON><PERSON><PERSON> v<PERSON> bất đồng bộ", "taskId": "ID tác vụ", "platform": "<PERSON><PERSON><PERSON> t<PERSON>", "type": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "progress": "<PERSON><PERSON><PERSON><PERSON> độ", "submitTime": "<PERSON><PERSON><PERSON><PERSON> gian g<PERSON>i", "startTime": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu", "endTime": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc", "duration": "<PERSON><PERSON><PERSON><PERSON> gian th<PERSON> hi<PERSON>n", "result": "<PERSON><PERSON><PERSON> qu<PERSON>", "taskIdPlaceholder": "<PERSON><PERSON> lòng nhập ID tác vụ", "platformPlaceholder": "<PERSON><PERSON><PERSON> n<PERSON>n tảng", "typePlaceholder": "<PERSON><PERSON>n lo<PERSON>i tác vụ", "statusPlaceholder": "<PERSON><PERSON><PERSON> trạng thái", "videoGeneration": "Tạo video", "imageGeneration": "<PERSON><PERSON><PERSON>", "musicGeneration": "Tạo nhạc", "textGeneration": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON> công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "inProgress": "<PERSON><PERSON> th<PERSON> hi<PERSON>n", "submitted": "Đ<PERSON> gửi", "queued": "<PERSON><PERSON> hàng", "notStarted": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu", "viewResult": "<PERSON><PERSON> kế<PERSON> quả", "viewError": "Xem lỗi", "taskDetails": "<PERSON> tiết tác vụ", "errorDetails": "<PERSON> tiết lỗi", "loadError": "<PERSON><PERSON><PERSON> danh sách tác vụ thất bại", "refreshSuccess": "<PERSON><PERSON><PERSON> mới trạng thái tác vụ thành công", "refreshFailed": "<PERSON><PERSON><PERSON> mới trạng thái tác vụ thất bại", "refreshError": "Lỗi khi làm mới trạng thái tác vụ", "viewVideo": "Xem video", "videoPreview": "<PERSON><PERSON> tr<PERSON> video", "copyVideoUrl": "<PERSON><PERSON> ch<PERSON>p địa chỉ video", "copiedVideoUrl": "<PERSON><PERSON> sao chép địa chỉ video", "downloadVideo": "T<PERSON><PERSON> video", "videoNotSupported": "Tr<PERSON><PERSON> của bạn không hỗ trợ phát video", "videoUrl": "Địa chỉ video", "videoUrls": "<PERSON><PERSON> s<PERSON>ch địa chỉ video"}}