package router

import (
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/controller"
	"github.com/songquanpeng/one-api/middleware"
)

func SetRelayRouter(router *gin.Engine) {
	router.Use(middleware.CORS())
	//router.Use(middleware.GzipDecodeMiddleware())

	// Gemini 官方格式路由支持 (需要在其他路由之前注册)
	SetGeminiOfficialRouter(router)

	// https://platform.openai.com/docs/api-reference/introduction
	modelsRouter := router.Group("/v1/models")
	modelsRouter.Use(middleware.LicenseAuth(), middleware.TokenAuth(), middleware.UserRelayRateLimit(), middleware.TokenRelayRateLimit())
	{
		modelsRouter.GET("", controller.ListCurrentUserAvailableModels)
		modelsRouter.GET("/:model", controller.RetrieveModel)
	}
	// 视频生成任务路由（基于new-api架构）
	videoTaskRouter := router.Group("/v1")
	videoTaskRouter.Use(middleware.RelayPanicRecover(), middleware.LicenseAuth(), middleware.JWTAuth(), middleware.TokenAuth(), middleware.UserRelayRateLimit(), middleware.TokenRelayRateLimit(), middleware.Distribute())
	{
		videoTaskRouter.POST("/video/generations", controller.RelayTask)
		videoTaskRouter.GET("/video/generations/:task_id", controller.RelayTask)
	}

	// 任务管理路由（不扣费）
	taskRouter := router.Group("/v1")
	taskRouter.Use(middleware.RelayPanicRecover(), middleware.LicenseAuth(), middleware.JWTAuth(), middleware.TokenAuth())
	{
		taskRouter.GET("/tasks/:task_id", controller.GetTaskStatus)
		taskRouter.POST("/tasks/:task_id/cancel", controller.CancelTask)
	}

	relayV1Router := router.Group("/v1")
	relayV1Router.Use(middleware.RelayPanicRecover(), middleware.LicenseAuth(), middleware.JWTAuth(), middleware.TokenAuth(), middleware.UserRelayRateLimit(), middleware.TokenRelayRateLimit(), middleware.Distribute(), middleware.UserTimeoutMiddleware())
	{
		relayV1Router.Any("/oneapi/proxy/:channelid/*target", controller.Relay)
		relayV1Router.POST("/completions", controller.Relay)
		// 支持claude路径调用
		relayV1Router.POST("/messages", controller.Relay)
		relayV1Router.POST("/chat/completions", controller.Relay)
		relayV1Router.POST("/edits", controller.Relay)
		relayV1Router.POST("/images/generations", controller.Relay)
		relayV1Router.POST("/images/edits", controller.Relay)
		// relayV1Router.POST("/videos/generations", controller.Relay) // 已被new-api风格的/video/generations替代
		relayV1Router.POST("/images/variations", controller.RelayNotImplemented)
		relayV1Router.POST("/embeddings", controller.Relay)
		relayV1Router.POST("/engines/:model/embeddings", controller.Relay)
		relayV1Router.POST("/audio/transcriptions", controller.Relay)
		relayV1Router.POST("/audio/translations", controller.Relay)
		relayV1Router.POST("/audio/speech", controller.Relay)
		relayV1Router.GET("/files", controller.RelayNotImplemented)
		relayV1Router.POST("/files", controller.RelayNotImplemented)
		relayV1Router.DELETE("/files/:id", controller.RelayNotImplemented)
		relayV1Router.GET("/files/:id", controller.RelayNotImplemented)
		relayV1Router.GET("/files/:id/content", controller.RelayNotImplemented)
		relayV1Router.POST("/fine_tuning/jobs", controller.RelayNotImplemented)
		relayV1Router.GET("/fine_tuning/jobs", controller.RelayNotImplemented)
		relayV1Router.GET("/fine_tuning/jobs/:id", controller.RelayNotImplemented)
		relayV1Router.POST("/fine_tuning/jobs/:id/cancel", controller.RelayNotImplemented)
		relayV1Router.GET("/fine_tuning/jobs/:id/events", controller.RelayNotImplemented)
		relayV1Router.DELETE("/models/:model", controller.RelayNotImplemented)
		relayV1Router.POST("/moderations", controller.Relay)
		relayV1Router.POST("/assistants", controller.RelayNotImplemented)
		relayV1Router.GET("/assistants/:id", controller.RelayNotImplemented)
		relayV1Router.POST("/assistants/:id", controller.RelayNotImplemented)
		relayV1Router.DELETE("/assistants/:id", controller.RelayNotImplemented)
		relayV1Router.GET("/assistants", controller.RelayNotImplemented)
		relayV1Router.POST("/assistants/:id/files", controller.RelayNotImplemented)
		relayV1Router.GET("/assistants/:id/files/:fileId", controller.RelayNotImplemented)
		relayV1Router.DELETE("/assistants/:id/files/:fileId", controller.RelayNotImplemented)
		relayV1Router.GET("/assistants/:id/files", controller.RelayNotImplemented)
		relayV1Router.POST("/threads", controller.RelayNotImplemented)
		relayV1Router.GET("/threads/:id", controller.RelayNotImplemented)
		relayV1Router.POST("/threads/:id", controller.RelayNotImplemented)
		relayV1Router.DELETE("/threads/:id", controller.RelayNotImplemented)
		relayV1Router.POST("/threads/:id/messages", controller.RelayNotImplemented)
		relayV1Router.GET("/threads/:id/messages/:messageId", controller.RelayNotImplemented)
		relayV1Router.POST("/threads/:id/messages/:messageId", controller.RelayNotImplemented)
		relayV1Router.GET("/threads/:id/messages/:messageId/files/:filesId", controller.RelayNotImplemented)
		relayV1Router.GET("/threads/:id/messages/:messageId/files", controller.RelayNotImplemented)
		relayV1Router.POST("/threads/:id/runs", controller.RelayNotImplemented)
		relayV1Router.GET("/threads/:id/runs/:runsId", controller.RelayNotImplemented)
		relayV1Router.POST("/threads/:id/runs/:runsId", controller.RelayNotImplemented)
		relayV1Router.GET("/threads/:id/runs", controller.RelayNotImplemented)
		relayV1Router.POST("/threads/:id/runs/:runsId/submit_tool_outputs", controller.RelayNotImplemented)
		relayV1Router.POST("/threads/:id/runs/:runsId/cancel", controller.RelayNotImplemented)
		relayV1Router.GET("/threads/:id/runs/:runsId/steps/:stepId", controller.RelayNotImplemented)
		relayV1Router.GET("/threads/:id/runs/:runsId/steps", controller.RelayNotImplemented)

		// 新增 responses API 支持
		relayV1Router.POST("/responses", controller.Relay)
		relayV1Router.GET("/responses/:id", controller.Relay)

		relayV1Router.GET("/realtime", controller.RelayRealtime)
	}
	relayCFV1Router := router.Group("/cf/v1")
	relayCFV1Router.Use(middleware.RelayPanicRecover(), middleware.LicenseAuth(), middleware.JWTAuth(), middleware.TokenAuth(), middleware.UserRelayRateLimit(), middleware.TokenRelayRateLimit())
	{
		relayCFV1Router.POST("/chat/completions", controller.CfRelay)
	}
	if config.MidjourneyPlusEnabled {
		relayMjPlusRouter := router.Group("/mj")
		registerMjRouterGroup(relayMjPlusRouter)
		relayMjPlusModeModeRouter := router.Group("/:mode/mj")
		registerMjRouterGroup(relayMjPlusModeModeRouter)
	} else {
		relayMjRouter := router.Group("/mj")
		relayMjRouter.GET("/image/:id", controller.RelayMidjourneyImage)
		relayMjRouter.Use(middleware.LicenseAuth(), middleware.JWTAuth(), middleware.TokenAuth(), middleware.UserRelayRateLimit(), middleware.TokenRelayRateLimit(), middleware.Distribute())
		{
			relayMjRouter.POST("/submit/imagine", controller.RelayMidjourney)
			relayMjRouter.POST("/submit/change", controller.RelayMidjourney)
			relayMjRouter.POST("/submit/describe", controller.RelayMidjourney)
			relayMjRouter.POST("/notify", controller.RelayMidjourney)
			relayMjRouter.GET("/task/:id/fetch", controller.RelayMidjourney)
		}
	}

	//relayMjRouter.Use()
	relaySearchRouter := router.Group("/search")
	relaySearchRouter.Use(middleware.LicenseAuth(), middleware.TokenAuth(), middleware.UserRelayRateLimit(), middleware.TokenRelayRateLimit(), middleware.Distribute())
	{
		relaySearchRouter.POST("/", controller.Relay) // https://github.com/yokingma/search_with_ai
		relaySearchRouter.POST("/serper", controller.Relay)
	}

	// Fish Audio API 路由
	relayFishRouter := router.Group("/fish")
	relayFishRouter.Use(middleware.LicenseAuth(), middleware.JWTAuth(), middleware.TokenAuth(), middleware.UserRelayRateLimit(), middleware.TokenRelayRateLimit(), middleware.Distribute())
	{
		// 创建模型API - 直接在/fish路径下
		relayFishRouter.POST("/model", controller.FishRelayCreateModel)

		// Fish Audio TTS和ASR API
		relayFishV1Router := relayFishRouter.Group("/v1")
		{
			relayFishV1Router.POST("/tts", controller.FishRelayTTS)
			relayFishV1Router.POST("/asr", controller.FishRelayASR)
		}
	}

	// Suno API 硬编码路由（与动态路由并存）
	relaySunoRouter := router.Group("/suno")
	relaySunoRouter.Use(middleware.LicenseAuth(), middleware.JWTAuth(), middleware.TokenAuth(), middleware.UserRelayRateLimit(), middleware.TokenRelayRateLimit(), middleware.Distribute())
	{
		relaySunoRouter.POST("/submit/:action", controller.RelaySuno)
		relaySunoRouter.POST("/fetch", controller.RelaySuno)
		relaySunoRouter.GET("/fetch/:id", controller.RelaySuno)
	}

	// 初始化动态路由
	err := controller.LoadDynamicMap(router)
	if err != nil {
		panic(err)
	}

	// 注册动态路由管理API
	controller.RegisterDynamicRouterAdminAPI(router)
}

func registerMjRouterGroup(rg *gin.RouterGroup) {
	rg.GET("/image/:id", controller.RelayMidjourneyPlusImage)
	rg.Use(middleware.LicenseAuth(), middleware.JWTAuth(), middleware.TokenAuth(), middleware.UserRelayRateLimit(), middleware.TokenRelayRateLimit(), middleware.Distribute())
	{
		rg.POST("/submit/action", controller.RelayMidjourneyPlus)
		rg.POST("/submit/modal", controller.RelayMidjourneyPlus)
		rg.POST("/submit/shorten", controller.RelayMidjourneyPlus)
		rg.POST("/submit/imagine", controller.RelayMidjourneyPlus)
		rg.POST("/submit/describe", controller.RelayMidjourneyPlus)
		rg.POST("/submit/blend", controller.RelayMidjourneyPlus)
		rg.POST("/submit/simple-change", controller.RelayMidjourneyPlus)
		rg.POST("/submit/change", controller.RelayMidjourneyPlus)
		rg.POST("/submit/video", controller.RelayMidjourneyPlus)
		rg.POST("/submit/edits", controller.RelayMidjourneyPlus)
		//rg.POST("/submit/upload-discord-images", controller.RelayMidjourneyPlus)
		rg.POST("/insight-face/swap", controller.RelayMidjourneyPlus)
		rg.GET("/task/:id/fetch", controller.RelayMidjourneyPlus)
		rg.GET("/task/:id/image-seed", controller.RelayMidjourneyPlus)
		rg.POST("/task/list-by-condition", controller.RelayMidjourneyPlus)
	}
}

// SetGeminiOfficialRouter 设置Gemini官方格式路由
func SetGeminiOfficialRouter(router *gin.Engine) {
	// v1beta 路由组
	v1betaRouter := router.Group("/v1beta")
	v1betaRouter.Use(middleware.RelayPanicRecover(), middleware.LicenseAuth(), middleware.JWTAuth(), middleware.TokenAuth(), middleware.UserRelayRateLimit(), middleware.TokenRelayRateLimit(), middleware.Distribute(), middleware.UserTimeoutMiddleware())
	{
		// 支持 Gemini 官方格式的路径
		// POST /v1beta/models/{model}:generateContent
		v1betaRouter.POST("/models/:model", controller.RelayGeminiOfficial)
		// GET /v1beta/models (列出模型)
		v1betaRouter.GET("/models", controller.ListCurrentUserAvailableModels)
		// GET /v1beta/models/{model} (获取特定模型信息)
		v1betaRouter.GET("/models/:model", controller.RetrieveModel)
	}

	// v1 路由组 (为了兼容性)
	v1GeminiRouter := router.Group("/v1")
	v1GeminiRouter.Use(middleware.RelayPanicRecover(), middleware.LicenseAuth(), middleware.JWTAuth(), middleware.TokenAuth(), middleware.UserRelayRateLimit(), middleware.TokenRelayRateLimit(), middleware.Distribute(), middleware.UserTimeoutMiddleware())
	{
		// 支持 v1 版本的 Gemini 官方格式
		v1GeminiRouter.POST("/models/:model", controller.RelayGeminiOfficial)
	}
}
