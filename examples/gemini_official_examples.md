# Gemini 官方格式 API 使用示例

本文档提供了使用 Gemini 官方格式 API 的详细示例。

## 基础配置

确保设置了正确的环境变量：
```bash
export GEMINI_VERSION=v1beta
```

## 1. 基本文本生成

### 请求示例
```bash
curl -X POST "https://your-api-domain.com/v1beta/models/gemini-2.5-pro:generateContent" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "解释一下什么是人工智能"
          }
        ]
      }
    ],
    "generationConfig": {
      "maxOutputTokens": 1000,
      "temperature": 0.7,
      "topP": 0.9
    }
  }'
```

### 响应示例
```json
{
  "id": "chatcmpl-xxx",
  "object": "chat.completion",
  "created": 1234567890,
  "model": "gemini-2.5-pro",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "人工智能（AI）是指..."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 10,
    "completion_tokens": 150,
    "total_tokens": 160
  }
}
```

## 2. 流式文本生成

### 请求示例
```bash
curl -X POST "https://your-api-domain.com/v1beta/models/gemini-2.5-pro:streamGenerateContent" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "写一首关于春天的诗"
          }
        ]
      }
    ],
    "generationConfig": {
      "maxOutputTokens": 500,
      "temperature": 0.8
    }
  }'
```

### 响应示例
```
data: {"id":"chatcmpl-xxx","object":"chat.completion.chunk","created":1234567890,"model":"gemini-2.5-pro","choices":[{"index":0,"delta":{"role":"assistant","content":"春"},"finish_reason":null}]}

data: {"id":"chatcmpl-xxx","object":"chat.completion.chunk","created":1234567890,"model":"gemini-2.5-pro","choices":[{"index":0,"delta":{"content":"天"},"finish_reason":null}]}

data: [DONE]
```

## 3. 多轮对话

### 请求示例
```bash
curl -X POST "https://your-api-domain.com/v1beta/models/gemini-2.5-pro:generateContent" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "你好，我想学习编程"
          }
        ]
      },
      {
        "role": "model",
        "parts": [
          {
            "text": "你好！学习编程是一个很好的选择。你想学习哪种编程语言呢？"
          }
        ]
      },
      {
        "role": "user",
        "parts": [
          {
            "text": "我想学Python，应该从哪里开始？"
          }
        ]
      }
    ]
  }'
```

## 4. 嵌入向量生成

### 请求示例
```bash
curl -X POST "https://your-api-domain.com/v1beta/models/text-embedding-004:batchEmbedContents" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "这是一个测试文本"
          }
        ]
      }
    ]
  }'
```

## 5. 错误处理示例

### 无效模型格式
```bash
curl -X POST "https://your-api-domain.com/v1beta/models/gemini-2.5-pro" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{}'
```

响应：
```json
{
  "error": {
    "message": "invalid model format, expected 'model:action'",
    "type": "invalid_request_error"
  }
}
```

### 不支持的操作
```bash
curl -X POST "https://your-api-domain.com/v1beta/models/gemini-2.5-pro:unsupportedAction" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{}'
```

响应：
```json
{
  "error": {
    "message": "unsupported action: unsupportedAction",
    "type": "invalid_request_error"
  }
}
```

## 6. JavaScript 示例

```javascript
// 基本文本生成
async function generateText() {
  const response = await fetch('https://your-api-domain.com/v1beta/models/gemini-2.5-pro:generateContent', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer YOUR_API_KEY'
    },
    body: JSON.stringify({
      contents: [
        {
          role: 'user',
          parts: [
            {
              text: 'Hello, how are you?'
            }
          ]
        }
      ],
      generationConfig: {
        maxOutputTokens: 100,
        temperature: 0.7
      }
    })
  });
  
  const data = await response.json();
  console.log(data);
}

// 流式生成
async function streamGenerate() {
  const response = await fetch('https://your-api-domain.com/v1beta/models/gemini-2.5-pro:streamGenerateContent', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer YOUR_API_KEY'
    },
    body: JSON.stringify({
      contents: [
        {
          role: 'user',
          parts: [
            {
              text: 'Tell me a story'
            }
          ]
        }
      ]
    })
  });
  
  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    
    const chunk = decoder.decode(value);
    console.log(chunk);
  }
}
```

## 7. Python 示例

```python
import requests
import json

# 基本文本生成
def generate_text():
    url = "https://your-api-domain.com/v1beta/models/gemini-2.5-pro:generateContent"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer YOUR_API_KEY"
    }
    data = {
        "contents": [
            {
                "role": "user",
                "parts": [
                    {
                        "text": "Hello, how are you?"
                    }
                ]
            }
        ],
        "generationConfig": {
            "maxOutputTokens": 100,
            "temperature": 0.7
        }
    }
    
    response = requests.post(url, headers=headers, json=data)
    return response.json()

# 流式生成
def stream_generate():
    url = "https://your-api-domain.com/v1beta/models/gemini-2.5-pro:streamGenerateContent"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer YOUR_API_KEY"
    }
    data = {
        "contents": [
            {
                "role": "user",
                "parts": [
                    {
                        "text": "Tell me a story"
                    }
                ]
            }
        ]
    }
    
    response = requests.post(url, headers=headers, json=data, stream=True)
    for line in response.iter_lines():
        if line:
            print(line.decode('utf-8'))

if __name__ == "__main__":
    # 测试基本生成
    result = generate_text()
    print(json.dumps(result, indent=2))
    
    # 测试流式生成
    stream_generate()
```

## 注意事项

1. **认证**: 所有请求都需要有效的 Bearer Token
2. **模型格式**: 必须使用 `model:action` 格式
3. **内容类型**: 请求头必须设置为 `application/json`
4. **流式响应**: 流式请求会返回 `text/event-stream` 格式的响应
5. **错误处理**: 系统会返回标准的错误格式，包含详细的错误信息

这些示例展示了如何使用 Gemini 官方格式 API 进行各种类型的请求。系统会自动将这些请求转换为内部格式并处理。
